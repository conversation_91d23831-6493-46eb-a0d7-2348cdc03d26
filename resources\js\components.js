// WIDDX AI UI Components - Modern Interactive Elements

class WiddxUIComponents {
    constructor() {
        this.initializeComponents();
    }

    initializeComponents() {
        this.setupTooltips();
        this.setupDropdowns();
        this.setupModals();
        this.setupNotifications();
        this.setupKeyboardShortcuts();
    }

    // ===== TOOLTIP SYSTEM =====
    setupTooltips() {
        const tooltipTriggers = document.querySelectorAll('[data-tooltip]');

        tooltipTriggers.forEach(trigger => {
            const tooltip = this.createTooltip(trigger.dataset.tooltip);

            trigger.addEventListener('mouseenter', (e) => {
                this.showTooltip(tooltip, e.target);
            });

            trigger.addEventListener('mouseleave', () => {
                this.hideTooltip(tooltip);
            });
        });
    }

    createTooltip(text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'widdx-tooltip';
        tooltip.textContent = text;
        tooltip.style.cssText = `
            position: absolute;
            background: var(--widdx-bg-elevated);
            color: var(--widdx-text-primary);
            padding: var(--widdx-space-xs) var(--widdx-space-sm);
            border-radius: var(--widdx-radius-sm);
            font-size: 0.75rem;
            white-space: nowrap;
            z-index: var(--widdx-z-tooltip);
            opacity: 0;
            pointer-events: none;
            transition: opacity var(--widdx-transition-fast);
            box-shadow: var(--widdx-shadow-md);
            border: 1px solid var(--widdx-border-primary);
        `;
        document.body.appendChild(tooltip);
        return tooltip;
    }

    showTooltip(tooltip, target) {
        const rect = target.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
        tooltip.style.opacity = '1';
    }

    hideTooltip(tooltip) {
        tooltip.style.opacity = '0';
        setTimeout(() => {
            if (tooltip.parentNode) {
                tooltip.parentNode.removeChild(tooltip);
            }
        }, 150);
    }

    // ===== DROPDOWN SYSTEM =====
    setupDropdowns() {
        const dropdownTriggers = document.querySelectorAll('[data-dropdown]');

        dropdownTriggers.forEach(trigger => {
            trigger.addEventListener('click', (e) => {
                e.stopPropagation();
                const dropdownId = trigger.dataset.dropdown;
                const dropdown = document.getElementById(dropdownId);
                this.toggleDropdown(dropdown, trigger);
            });
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', () => {
            this.closeAllDropdowns();
        });
    }

    toggleDropdown(dropdown, trigger) {
        const isOpen = dropdown.classList.contains('widdx-dropdown-open');

        this.closeAllDropdowns();

        if (!isOpen) {
            this.openDropdown(dropdown, trigger);
        }
    }

    openDropdown(dropdown, trigger) {
        const rect = trigger.getBoundingClientRect();
        dropdown.style.cssText = `
            position: absolute;
            top: ${rect.bottom + 8}px;
            left: ${rect.left}px;
            min-width: ${rect.width}px;
            background: var(--widdx-bg-elevated);
            border: 1px solid var(--widdx-border-primary);
            border-radius: var(--widdx-radius-md);
            box-shadow: var(--widdx-shadow-lg);
            z-index: var(--widdx-z-dropdown);
            opacity: 0;
            transform: translateY(-10px);
            transition: all var(--widdx-transition-fast);
        `;

        dropdown.classList.add('widdx-dropdown-open');

        // Animate in
        requestAnimationFrame(() => {
            dropdown.style.opacity = '1';
            dropdown.style.transform = 'translateY(0)';
        });
    }

    closeAllDropdowns() {
        const openDropdowns = document.querySelectorAll('.widdx-dropdown-open');
        openDropdowns.forEach(dropdown => {
            dropdown.style.opacity = '0';
            dropdown.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                dropdown.classList.remove('widdx-dropdown-open');
            }, 150);
        });
    }

    // ===== MODAL SYSTEM =====
    setupModals() {
        const modalTriggers = document.querySelectorAll('[data-modal]');

        modalTriggers.forEach(trigger => {
            trigger.addEventListener('click', () => {
                const modalId = trigger.dataset.modal;
                const modal = document.getElementById(modalId);
                this.openModal(modal);
            });
        });

        // Close modal on backdrop click
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('widdx-modal-backdrop')) {
                this.closeModal(e.target.closest('.widdx-modal'));
            }
        });

        // Close modal on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.widdx-modal.widdx-modal-open');
                if (openModal) {
                    this.closeModal(openModal);
                }
            }
        });
    }

    openModal(modal) {
        modal.classList.add('widdx-modal-open');
        document.body.style.overflow = 'hidden';

        // Focus management
        const firstFocusable = modal.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
        if (firstFocusable) {
            firstFocusable.focus();
        }
    }

    closeModal(modal) {
        modal.classList.remove('widdx-modal-open');
        document.body.style.overflow = '';
    }

    // ===== NOTIFICATION SYSTEM =====
    setupNotifications() {
        this.notificationContainer = this.createNotificationContainer();
    }

    createNotificationContainer() {
        const container = document.createElement('div');
        container.className = 'widdx-notification-container';
        container.style.cssText = `
            position: fixed;
            top: var(--widdx-space-lg);
            right: var(--widdx-space-lg);
            z-index: var(--widdx-z-modal);
            display: flex;
            flex-direction: column;
            gap: var(--widdx-space-sm);
            max-width: 400px;
        `;
        document.body.appendChild(container);
        return container;
    }

    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `widdx-notification widdx-notification-${type}`;

        const colors = {
            success: 'var(--widdx-success)',
            error: 'var(--widdx-error)',
            warning: 'var(--widdx-warning)',
            info: 'var(--widdx-info)'
        };

        notification.style.cssText = `
            background: var(--widdx-bg-elevated);
            border: 1px solid var(--widdx-border-primary);
            border-left: 4px solid ${colors[type]};
            border-radius: var(--widdx-radius-md);
            padding: var(--widdx-space-md);
            box-shadow: var(--widdx-shadow-lg);
            transform: translateX(100%);
            transition: transform var(--widdx-transition-normal);
            color: var(--widdx-text-primary);
        `;

        notification.innerHTML = `
            <div class="widdx-flex widdx-items-center widdx-gap-sm">
                <span class="widdx-notification-icon">${this.getNotificationIcon(type)}</span>
                <span class="widdx-notification-message">${message}</span>
                <button class="widdx-notification-close" style="margin-left: auto; background: none; border: none; color: var(--widdx-text-secondary); cursor: pointer;">×</button>
            </div>
        `;

        this.notificationContainer.appendChild(notification);

        // Animate in
        requestAnimationFrame(() => {
            notification.style.transform = 'translateX(0)';
        });

        // Close button
        notification.querySelector('.widdx-notification-close').addEventListener('click', () => {
            this.hideNotification(notification);
        });

        // Auto hide
        if (duration > 0) {
            setTimeout(() => {
                this.hideNotification(notification);
            }, duration);
        }

        return notification;
    }

    hideNotification(notification) {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 250);
    }

    getNotificationIcon(type) {
        const icons = {
            success: '✓',
            error: '✕',
            warning: '⚠',
            info: 'ℹ'
        };
        return icons[type] || icons.info;
    }

    // ===== KEYBOARD SHORTCUTS =====
    setupKeyboardShortcuts() {
        this.shortcuts = new Map();

        document.addEventListener('keydown', (e) => {
            const key = this.getShortcutKey(e);
            const handler = this.shortcuts.get(key);

            if (handler) {
                e.preventDefault();
                handler();
            }
        });
    }

    registerShortcut(keys, handler) {
        this.shortcuts.set(keys, handler);
    }

    getShortcutKey(e) {
        const parts = [];
        if (e.ctrlKey) parts.push('ctrl');
        if (e.altKey) parts.push('alt');
        if (e.shiftKey) parts.push('shift');
        if (e.metaKey) parts.push('meta');
        parts.push(e.key.toLowerCase());
        return parts.join('+');
    }

    // ===== UTILITY METHODS =====
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// Initialize components when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.widdxUI = new WiddxUIComponents();
});

// Chat Area Controller
class WiddxChatArea {
    constructor() {
        this.messagesContainer = document.getElementById('messages-list');
        this.typingIndicator = document.getElementById('typing-indicator');
        this.scrollBtn = document.getElementById('scroll-to-bottom');
        this.featuresPanel = document.getElementById('features-panel');
        this.featuresToggle = document.getElementById('features-toggle');

        this.messages = [];
        this.isTyping = false;
        this.autoScroll = true;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupScrollHandling();
        this.setupFeatureButtons();
        this.setupSuggestions();
    }

    setupEventListeners() {
        // Features toggle
        this.featuresToggle?.addEventListener('click', () => {
            this.toggleFeatures();
        });

        // Scroll to bottom button
        this.scrollBtn?.addEventListener('click', () => {
            this.scrollToBottom(true);
        });

        // Message actions
        document.addEventListener('click', (e) => {
            if (e.target.closest('.widdx-message-action')) {
                const action = e.target.closest('.widdx-message-action').dataset.action;
                const messageElement = e.target.closest('.widdx-message');
                this.handleMessageAction(action, messageElement);
            }
        });
    }

    setupScrollHandling() {
        if (!this.messagesContainer) return;

        this.messagesContainer.addEventListener('scroll', this.debounce(() => {
            const { scrollTop, scrollHeight, clientHeight } = this.messagesContainer;
            const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;

            this.autoScroll = isNearBottom;

            if (this.scrollBtn) {
                this.scrollBtn.classList.toggle('hidden', isNearBottom);
            }
        }, 100));
    }

    setupFeatureButtons() {
        const featureButtons = document.querySelectorAll('.widdx-feature-btn');
        featureButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const feature = btn.dataset.feature;
                this.activateFeature(feature);
            });
        });
    }

    setupSuggestions() {
        const suggestionButtons = document.querySelectorAll('.widdx-suggestion-btn');
        suggestionButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const suggestion = btn.dataset.suggestion;
                this.sendSuggestion(suggestion);
            });
        });
    }

    toggleFeatures() {
        if (!this.featuresPanel) return;

        const isHidden = this.featuresPanel.classList.contains('hidden');
        this.featuresPanel.classList.toggle('hidden', !isHidden);

        // Update toggle button state
        if (this.featuresToggle) {
            this.featuresToggle.classList.toggle('widdx-btn-primary', isHidden);
            this.featuresToggle.classList.toggle('widdx-btn-secondary', !isHidden);
        }
    }

    activateFeature(feature) {
        // Remove active state from all buttons
        document.querySelectorAll('.widdx-feature-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // Add active state to clicked button
        const activeBtn = document.querySelector(`[data-feature="${feature}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }

        // Emit feature activation event
        const event = new CustomEvent('widdx-feature-activated', {
            detail: { feature }
        });
        document.dispatchEvent(event);
    }

    sendSuggestion(suggestion) {
        // Hide welcome message
        const welcomeMessage = document.querySelector('.widdx-welcome-message');
        if (welcomeMessage) {
            welcomeMessage.style.display = 'none';
        }

        // Emit suggestion event
        const event = new CustomEvent('widdx-send-message', {
            detail: { message: suggestion }
        });
        document.dispatchEvent(event);
    }

    addMessage(type, content, options = {}) {
        const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        const timestamp = new Date().toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });

        const messageData = {
            id: messageId,
            type,
            content,
            timestamp,
            status: options.status || 'sent',
            ...options
        };

        this.messages.push(messageData);
        this.renderMessage(messageData);

        if (this.autoScroll) {
            this.scrollToBottom();
        }

        return messageId;
    }

    renderMessage(messageData) {
        const messageElement = document.createElement('div');
        messageElement.className = `widdx-message widdx-message-${messageData.type} animate-slide-up`;
        messageElement.dataset.messageId = messageData.id;
        messageElement.dataset.messageType = messageData.type;

        messageElement.innerHTML = this.getMessageHTML(messageData);

        if (this.messagesContainer) {
            this.messagesContainer.appendChild(messageElement);
        }
    }

    // Helper function to detect RTL languages
    isRTL(text) {
        if (!text || typeof text !== 'string') return false;
        // Match any RTL character in the first 100 characters
        return /[\u0591-\u07FF\uFB1D-\uFEFE\u0800-\u08FF\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\u0590-\u05FF]/.test(text.substring(0, 100));
    }

    getMessageHTML(messageData) {
        const { type, content, timestamp, status } = messageData;
        const isRTL = this.isRTL(content);
        const direction = isRTL ? 'rtl' : 'ltr';
        const textAlign = isRTL ? 'right' : 'left';
        const messageClass = isRTL ? 'rtl-message' : 'ltr-message';

        // Inline styles to ensure they take precedence
        const containerStyle = `
            display: flex;
            flex-direction: row;
            direction: ${direction} !important;
            unicode-bidi: plaintext;
            text-align: ${textAlign} !important;
            margin-bottom: 1rem;
        `;

        const contentStyle = `
            flex: 1;
            direction: ${direction} !important;
            text-align: ${textAlign} !important;
            unicode-bidi: plaintext;
        `;

        const textStyle = `
            direction: ${direction} !important;
            text-align: ${textAlign} !important;
            unicode-bidi: plaintext;
            display: inline-block;
            width: 100%;
        `;

        return `
            <div class="widdx-message-container ${messageClass}" style="${containerStyle}">
                <div class="widdx-message-avatar">
                    ${type === 'user' ? this.getUserAvatarHTML() : this.getAIAvatarHTML()}
                </div>
                <div class="widdx-message-content" style="${contentStyle}">
                    <div class="widdx-message-header">
                        <span class="widdx-message-sender">${type === 'user' ? 'أنت' : 'WIDDX AI'}</span>
                        <span class="widdx-message-time">${timestamp}</span>
                        ${type === 'user' ? this.getStatusHTML(status) : ''}
                    </div>
                    <div class="widdx-message-body" style="direction: ${direction} !important; text-align: ${textAlign} !important;">
                        <div class="widdx-message-text" style="${textStyle}">${content}</div>
                    </div>
                    <div class="widdx-message-actions" style="direction: ltr !important;">
                        ${this.getMessageActionsHTML(type)}
                    </div>
                </div>
            </div>
        `;
    }

    getUserAvatarHTML() {
        return `
            <div class="widdx-avatar widdx-avatar-user">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                </svg>
            </div>
        `;
    }

    getAIAvatarHTML() {
        return `
            <div class="widdx-avatar widdx-avatar-ai">
                <div class="widdx-gradient-primary rounded-full w-8 h-8 flex items-center justify-center">
                    <span class="text-white font-bold text-sm">W</span>
                </div>
            </div>
        `;
    }

    getStatusHTML(status) {
        const icons = {
            sending: '<svg class="w-3 h-3 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>',
            sent: '<svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>',
            error: '<svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>'
        };

        return `<span class="widdx-message-status widdx-message-status-${status}">${icons[status] || ''}</span>`;
    }

    getMessageActionsHTML(type) {
        if (type === 'user') {
            return `
                <button class="widdx-message-action" data-action="edit" data-tooltip="تعديل الرسالة">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                </button>
            `;
        } else {
            return `
                <button class="widdx-message-action" data-action="copy" data-tooltip="نسخ الرسالة">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                </button>
                <button class="widdx-message-action" data-action="regenerate" data-tooltip="إعادة توليد">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </button>
                <button class="widdx-message-action" data-action="like" data-tooltip="إعجاب">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
                    </svg>
                </button>
                <button class="widdx-message-action" data-action="dislike" data-tooltip="عدم إعجاب">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14H5.236a2 2 0 01-1.789-2.894l3.5-7A2 2 0 018.736 3h4.018c.163 0 .326.02.485.06L17 4m-7 10v5a2 2 0 002 2h.095c.5 0 .905-.405.905-.905 0-.714.211-1.412.608-2.006L17 13V4m-7 10h2m5-10h2a2 2 0 012 2v6a2 2 0 01-2 2h-2.5"></path>
                    </svg>
                </button>
            `;
        }
    }

    handleMessageAction(action, messageElement) {
        const messageId = messageElement.dataset.messageId;
        const messageData = this.messages.find(m => m.id === messageId);

        switch (action) {
            case 'copy':
                this.copyMessage(messageData);
                break;
            case 'regenerate':
                this.regenerateMessage(messageData);
                break;
            case 'edit':
                this.editMessage(messageData);
                break;
            case 'like':
            case 'dislike':
                this.rateMessage(messageData, action);
                break;
        }
    }

    copyMessage(messageData) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(messageData.content).then(() => {
                if (window.widdxUI) {
                    window.widdxUI.showNotification('تم نسخ الرسالة', 'success', 2000);
                }
            });
        }
    }

    regenerateMessage(messageData) {
        // Emit regenerate event
        const event = new CustomEvent('widdx-regenerate-message', {
            detail: { messageId: messageData.id }
        });
        document.dispatchEvent(event);
    }

    editMessage(messageData) {
        // Emit edit event
        const event = new CustomEvent('widdx-edit-message', {
            detail: { messageId: messageData.id, content: messageData.content }
        });
        document.dispatchEvent(event);
    }

    rateMessage(messageData, rating) {
        // Emit rating event
        const event = new CustomEvent('widdx-rate-message', {
            detail: { messageId: messageData.id, rating }
        });
        document.dispatchEvent(event);

        if (window.widdxUI) {
            const message = rating === 'like' ? 'شكراً لتقييمك الإيجابي' : 'شكراً لملاحظاتك';
            window.widdxUI.showNotification(message, 'info', 2000);
        }
    }

    showTyping() {
        this.isTyping = true;
        if (this.typingIndicator) {
            this.typingIndicator.classList.remove('hidden');
        }
        if (this.autoScroll) {
            this.scrollToBottom();
        }
    }

    hideTyping() {
        this.isTyping = false;
        if (this.typingIndicator) {
            this.typingIndicator.classList.add('hidden');
        }
    }

    scrollToBottom(force = false) {
        if (!this.messagesContainer) return;

        if (force || this.autoScroll) {
            this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
        }
    }

    clearMessages() {
        this.messages = [];
        if (this.messagesContainer) {
            this.messagesContainer.innerHTML = '';
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { WiddxUIComponents, WiddxChatArea };
}
