[2025-07-11 23:32:57] local.INFO: Language detection {"message":"Generate a beautiful image","detected_code":"it","max_score":0.300710504727498,"result":{"it":0.300710504727498,"da":0.2954031159941477,"id":0.2951265295189976,"ro":0.29384906863249544,"no":0.2921475614989603,"de":0.29140174881287223,"nl":0.2894985494661803,"tl":0.2877216986625887,"af":0.2843412322234357,"fr":0.28103832502771686,"sw":0.2777130999555199,"es":0.27705165043945174,"en":0.2740402145925896,"sv":0.27173003992831873,"tr":0.2669085420260271,"pt":0.2654312740993157,"fi":0.2644339182596075,"so":0.2587713787085515,"et":0.25869756393018944,"hr":0.25488117350730044,"sq":0.24657151820493955,"hu":0.24589831730396441,"sl":0.23837588596856954,"lt":0.23459484087118654,"i-klingon":0.23391949523865163,"lv":0.22650845131393452,"sk":0.2176578236942366,"cs":0.2050076725524286,"pl":0.20483668720257384,"vi":0.17300622274530766,"th":0.015519755270958712,"pa":0.015490802981794984,"te":0.014253513825323938,"he":0.012810849327638516,"bn":0.012074612730815672,"ml":0.011507370190803879,"el":0.01096750298464403,"bg":0.010861156262206875,"mk":0.009272928870133531,"ta":0.009115831253934033,"ru":0.007282307141006884,"kn":0.005191652764078147,"uk":0.004574925339041736,"gu":0.0009867523544087604,"ar":0,"fa":0,"hi":0,"ja":0,"ko":0,"mr":0,"ne":0,"ur":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 23:32:58] local.INFO: Image generation started {"prompt":"a beautiful image","provider":"gemini","options":{"personality_prompt":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative.","target_language":"italian","target_language_code":"it","language_confidence":0.300710504727498,"max_tokens":2000,"temperature":0.7,"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}} 
[2025-07-11 23:33:03] local.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content","finishReason","index"]} 
[2025-07-11 23:33:03] local.INFO: Text response from Gemini {"text":"I will generate a beautiful image with a realistic style, using natural lighting to highlight high levels of detail, ensuring good image quality.

"} 
[2025-07-11 23:33:03] local.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-33-03_5baefc30.png","size":1300345,"size_formatted":"1.24 MB","metadata":{"mime_type":"image/png","prompt":"a beautiful image","options":{"personality_prompt":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative.","target_language":"italian","target_language_code":"it","language_confidence":0.300710504727498,"max_tokens":2000,"temperature":0.7,"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}}} 
[2025-07-11 23:33:03] local.INFO: Image processed successfully {"part_index":1,"file_size":1300345,"path":"generated_images/gemini_2025-07-11_23-33-03_5baefc30.png"} 
[2025-07-11 23:33:03] local.INFO: Image generation completed {"prompt":"a beautiful image","provider":"gemini","success":true,"images_count":1} 
[2025-07-11 23:33:03] local.ERROR: Chat Controller Error {"message":"Undefined array key \"success\"","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined array...', 'C:\\\\Users\\\\<USER>\\\\...', 96)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(96): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined array...', 'C:\\\\Users\\\\<USER>\\\\...', 96)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(App\\Http\\Requests\\ChatRequest))
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#42 {main}","request_data":{"message":"Generate a beautiful image","session_id":"test-session-image","personality":"neutral","think_mode":false}} 
[2025-07-11 23:33:04] local.INFO: Language detection {"message":"مرحبا","detected_code":"ar","max_score":0.**********5835791,"result":{"ar":0.**********5835791,"fa":0.10429469856083184,"ur":0.09534008946245603,"so":0.0009493891797556719,"he":0.00024660361013562564,"et":8.336394469270356e-5,"tr":7.585657312061947e-5,"te":6.207755047068215e-5,"sw":5.9372756513809855e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"ro":4.5951028945866016e-5,"id":4.409251662944479e-5,"ko":4.0264005391582206e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"lv":3.423586686356094e-5,"sl":3.357427000042656e-5,"lt":1.7519586205815487e-5,"vi":1.7137537158470558e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 23:33:09] local.ERROR: Gemini API Error {"status":429,"body":"{
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"location\": \"global\",
              \"model\": \"gemini-1.5-flash\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"45s\"
      }
    ]
  }
}
"} 
[2025-07-11 23:33:09] local.ERROR: Gemini Client Error {"message":"Gemini API request failed: {
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"location\": \"global\",
              \"model\": \"gemini-1.5-flash\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"45s\"
      }
    ]
  }
}
","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(225): App\\Services\\GeminiClient->chat(Array, Array)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(83): App\\Services\\ModelMergerService->getModelResponses('You are WIDDX, ...', '\\xD9\\x85\\xD8\\xB1\\xD8\\xAD\\xD8\\xA8\\xD8\\xA7', Array, Array)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(90): App\\Services\\ModelMergerService->processMessage('\\xD9\\x85\\xD8\\xB1\\xD8\\xAD\\xD8\\xA8\\xD8\\xA7', Array, Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(App\\Http\\Requests\\ChatRequest))
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#43 {main}"} 
[2025-07-11 23:33:10] local.ERROR: Gemini API Error {"status":429,"body":"{
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"location\": \"global\",
              \"model\": \"gemini-1.5-flash\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"44s\"
      }
    ]
  }
}
"} 
[2025-07-11 23:33:10] local.ERROR: Gemini Client Error {"message":"Gemini API request failed: {
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"location\": \"global\",
              \"model\": \"gemini-1.5-flash\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"44s\"
      }
    ]
  }
}
","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(225): App\\Services\\GeminiClient->chat(Array, Array)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(83): App\\Services\\ModelMergerService->getModelResponses('You are WIDDX, ...', '\\xD9\\x85\\xD8\\xB1\\xD8\\xAD\\xD8\\xA8\\xD8\\xA7', Array, Array)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(90): App\\Services\\ModelMergerService->processMessage('\\xD9\\x85\\xD8\\xB1\\xD8\\xAD\\xD8\\xA8\\xD8\\xA7', Array, Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(App\\Http\\Requests\\ChatRequest))
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#43 {main}"} 
[2025-07-11 23:33:10] local.ERROR: Gemini API Error {"status":429,"body":"{
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"location\": \"global\",
              \"model\": \"gemini-1.5-flash\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"44s\"
      }
    ]
  }
}
"} 
[2025-07-11 23:33:10] local.ERROR: Gemini Client Error {"message":"Gemini API request failed: {
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"location\": \"global\",
              \"model\": \"gemini-1.5-flash\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"44s\"
      }
    ]
  }
}
","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(225): App\\Services\\GeminiClient->chat(Array, Array)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(83): App\\Services\\ModelMergerService->getModelResponses('You are WIDDX, ...', '\\xD9\\x85\\xD8\\xB1\\xD8\\xAD\\xD8\\xA8\\xD8\\xA7', Array, Array)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(90): App\\Services\\ModelMergerService->processMessage('\\xD9\\x85\\xD8\\xB1\\xD8\\xAD\\xD8\\xA8\\xD8\\xA7', Array, Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(App\\Http\\Requests\\ChatRequest))
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#43 {main}"} 
[2025-07-11 23:33:23] local.INFO: Language detection {"message":"Generate a beautiful image","detected_code":"it","max_score":0.300710504727498,"result":{"it":0.300710504727498,"da":0.2954031159941477,"id":0.2951265295189976,"ro":0.29384906863249544,"no":0.2921475614989603,"de":0.29140174881287223,"nl":0.2894985494661803,"tl":0.2877216986625887,"af":0.2843412322234357,"fr":0.28103832502771686,"sw":0.2777130999555199,"es":0.27705165043945174,"en":0.2740402145925896,"sv":0.27173003992831873,"tr":0.2669085420260271,"pt":0.2654312740993157,"fi":0.2644339182596075,"so":0.2587713787085515,"et":0.25869756393018944,"hr":0.25488117350730044,"sq":0.24657151820493955,"hu":0.24589831730396441,"sl":0.23837588596856954,"lt":0.23459484087118654,"i-klingon":0.23391949523865163,"lv":0.22650845131393452,"sk":0.2176578236942366,"cs":0.2050076725524286,"pl":0.20483668720257384,"vi":0.17300622274530766,"th":0.015519755270958712,"pa":0.015490802981794984,"te":0.014253513825323938,"he":0.012810849327638516,"bn":0.012074612730815672,"ml":0.011507370190803879,"el":0.01096750298464403,"bg":0.010861156262206875,"mk":0.009272928870133531,"ta":0.009115831253934033,"ru":0.007282307141006884,"kn":0.005191652764078147,"uk":0.004574925339041736,"gu":0.0009867523544087604,"ar":0,"fa":0,"hi":0,"ja":0,"ko":0,"mr":0,"ne":0,"ur":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 23:33:23] local.INFO: Image generation started {"prompt":"a beautiful image","provider":"gemini","options":{"personality_prompt":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative.","target_language":"italian","target_language_code":"it","language_confidence":0.300710504727498,"max_tokens":2000,"temperature":0.7,"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}} 
[2025-07-11 23:33:29] local.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content","finishReason","index"]} 
[2025-07-11 23:33:29] local.INFO: Text response from Gemini {"text":"I will generate a photorealistic image with soft, natural sunlight illuminating the scene, rendered with a high level of detail and overall good image quality.
"} 
[2025-07-11 23:33:29] local.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-33-29_bd22bd57.png","size":742258,"size_formatted":"724.86 KB","metadata":{"mime_type":"image/png","prompt":"a beautiful image","options":{"personality_prompt":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative.","target_language":"italian","target_language_code":"it","language_confidence":0.300710504727498,"max_tokens":2000,"temperature":0.7,"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}}} 
[2025-07-11 23:33:29] local.INFO: Image processed successfully {"part_index":1,"file_size":742258,"path":"generated_images/gemini_2025-07-11_23-33-29_bd22bd57.png"} 
[2025-07-11 23:33:29] local.INFO: Image generation completed {"prompt":"a beautiful image","provider":"gemini","success":true,"images_count":1} 
[2025-07-11 23:33:29] local.ERROR: Chat Controller Error {"message":"Undefined array key \"success\"","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined array...', 'C:\\\\Users\\\\<USER>\\\\...', 96)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(96): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined array...', 'C:\\\\Users\\\\<USER>\\\\...', 96)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(App\\Http\\Requests\\ChatRequest))
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#42 {main}","request_data":{"message":"Generate a beautiful image","session_id":"session_1752276757843_6rtw2m57q","personality":"neutral","think_mode":false}} 
[2025-07-11 23:33:50] local.INFO: Language detection {"message":"قم بإنشاء صور لرجل يركب جمل","detected_code":"ar","max_score":0.2597325482276696,"result":{"ar":0.2597325482276696,"fa":0.22470844875000984,"ur":0.22449661385070743,"so":0.001787085514834206,"he":0.00042236752683104314,"tr":0.00019617067362140859,"et":0.00015097641320461108,"te":0.00010273653089592427,"sw":0.00010019152661705413,"ro":8.116727970101713e-5,"ko":8.031720447221372e-5,"lv":6.329831384551712e-5,"sl":6.27453570499775e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"vi":3.23434515179523e-5,"lt":3.1760653355571936e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 23:34:02] local.ERROR: Gemini API Error {"status":429,"body":"{
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"model\": \"gemini-1.5-flash\",
              \"location\": \"global\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"53s\"
      }
    ]
  }
}
"} 
[2025-07-11 23:34:02] local.ERROR: Gemini Client Error {"message":"Gemini API request failed: {
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"model\": \"gemini-1.5-flash\",
              \"location\": \"global\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"53s\"
      }
    ]
  }
}
","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(225): App\\Services\\GeminiClient->chat(Array, Array)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(83): App\\Services\\ModelMergerService->getModelResponses('You are WIDDX, ...', '\\xD9\\x82\\xD9\\x85 \\xD8\\xA8\\xD8\\xA5\\xD9\\x86\\xD8\\xB4\\xD8\\xA7...', Array, Array)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(90): App\\Services\\ModelMergerService->processMessage('\\xD9\\x82\\xD9\\x85 \\xD8\\xA8\\xD8\\xA5\\xD9\\x86\\xD8\\xB4\\xD8\\xA7...', Array, Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(App\\Http\\Requests\\ChatRequest))
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#43 {main}"} 
[2025-07-11 23:34:02] local.ERROR: Gemini API Error {"status":429,"body":"{
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"location\": \"global\",
              \"model\": \"gemini-1.5-flash\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"52s\"
      }
    ]
  }
}
"} 
[2025-07-11 23:34:02] local.ERROR: Gemini Client Error {"message":"Gemini API request failed: {
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"location\": \"global\",
              \"model\": \"gemini-1.5-flash\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"52s\"
      }
    ]
  }
}
","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(225): App\\Services\\GeminiClient->chat(Array, Array)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(83): App\\Services\\ModelMergerService->getModelResponses('You are WIDDX, ...', '\\xD9\\x82\\xD9\\x85 \\xD8\\xA8\\xD8\\xA5\\xD9\\x86\\xD8\\xB4\\xD8\\xA7...', Array, Array)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(90): App\\Services\\ModelMergerService->processMessage('\\xD9\\x82\\xD9\\x85 \\xD8\\xA8\\xD8\\xA5\\xD9\\x86\\xD8\\xB4\\xD8\\xA7...', Array, Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(App\\Http\\Requests\\ChatRequest))
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#43 {main}"} 
[2025-07-11 23:34:03] local.ERROR: Gemini API Error {"status":429,"body":"{
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"model\": \"gemini-1.5-flash\",
              \"location\": \"global\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"52s\"
      }
    ]
  }
}
"} 
[2025-07-11 23:34:03] local.ERROR: Gemini Client Error {"message":"Gemini API request failed: {
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"model\": \"gemini-1.5-flash\",
              \"location\": \"global\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"52s\"
      }
    ]
  }
}
","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(225): App\\Services\\GeminiClient->chat(Array, Array)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(83): App\\Services\\ModelMergerService->getModelResponses('You are WIDDX, ...', '\\xD9\\x82\\xD9\\x85 \\xD8\\xA8\\xD8\\xA5\\xD9\\x86\\xD8\\xB4\\xD8\\xA7...', Array, Array)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(90): App\\Services\\ModelMergerService->processMessage('\\xD9\\x82\\xD9\\x85 \\xD8\\xA8\\xD8\\xA5\\xD9\\x86\\xD8\\xB4\\xD8\\xA7...', Array, Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(App\\Http\\Requests\\ChatRequest))
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#43 {main}"} 
[2025-07-11 23:34:10] local.INFO: Language detection {"message":"Generate a beautiful image","detected_code":"it","max_score":0.300710504727498,"result":{"it":0.300710504727498,"da":0.2954031159941477,"id":0.2951265295189976,"ro":0.29384906863249544,"no":0.2921475614989603,"de":0.29140174881287223,"nl":0.2894985494661803,"tl":0.2877216986625887,"af":0.2843412322234357,"fr":0.28103832502771686,"sw":0.2777130999555199,"es":0.27705165043945174,"en":0.2740402145925896,"sv":0.27173003992831873,"tr":0.2669085420260271,"pt":0.2654312740993157,"fi":0.2644339182596075,"so":0.2587713787085515,"et":0.25869756393018944,"hr":0.25488117350730044,"sq":0.24657151820493955,"hu":0.24589831730396441,"sl":0.23837588596856954,"lt":0.23459484087118654,"i-klingon":0.23391949523865163,"lv":0.22650845131393452,"sk":0.2176578236942366,"cs":0.2050076725524286,"pl":0.20483668720257384,"vi":0.17300622274530766,"th":0.015519755270958712,"pa":0.015490802981794984,"te":0.014253513825323938,"he":0.012810849327638516,"bn":0.012074612730815672,"ml":0.011507370190803879,"el":0.01096750298464403,"bg":0.010861156262206875,"mk":0.009272928870133531,"ta":0.009115831253934033,"ru":0.007282307141006884,"kn":0.005191652764078147,"uk":0.004574925339041736,"gu":0.0009867523544087604,"ar":0,"fa":0,"hi":0,"ja":0,"ko":0,"mr":0,"ne":0,"ur":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 23:34:10] local.INFO: Image generation started {"prompt":"a beautiful image","provider":"gemini","options":{"personality_prompt":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative.","target_language":"italian","target_language_code":"it","language_confidence":0.300710504727498,"max_tokens":2000,"temperature":0.7,"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}} 
[2025-07-11 23:34:15] local.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content","finishReason","index"]} 
[2025-07-11 23:34:15] local.INFO: Text response from Gemini {"text":"I will generate a photorealistic image with natural lighting and a high level of detail, ensuring good image quality. The scene will be beautifully composed.
"} 
[2025-07-11 23:34:15] local.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-34-15_04c0587e.png","size":1376939,"size_formatted":"1.31 MB","metadata":{"mime_type":"image/png","prompt":"a beautiful image","options":{"personality_prompt":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative.","target_language":"italian","target_language_code":"it","language_confidence":0.300710504727498,"max_tokens":2000,"temperature":0.7,"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}}} 
[2025-07-11 23:34:15] local.INFO: Image processed successfully {"part_index":1,"file_size":1376939,"path":"generated_images/gemini_2025-07-11_23-34-15_04c0587e.png"} 
[2025-07-11 23:34:15] local.INFO: Image generation completed {"prompt":"a beautiful image","provider":"gemini","success":true,"images_count":1} 
[2025-07-11 23:34:15] local.ERROR: Chat Controller Error {"message":"Undefined array key \"success\"","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined array...', 'C:\\\\Users\\\\<USER>\\\\...', 96)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(96): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined array...', 'C:\\\\Users\\\\<USER>\\\\...', 96)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(App\\Http\\Requests\\ChatRequest))
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#42 {main}","request_data":{"message":"Generate a beautiful image","session_id":"test-session-image","personality":"neutral","think_mode":false}} 
[2025-07-11 23:34:56] local.INFO: Image generation started {"prompt":"a beautiful image","provider":"gemini","options":{"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}} 
[2025-07-11 23:35:00] local.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content","finishReason","index"]} 
[2025-07-11 23:35:00] local.INFO: Text response from Gemini {"text":"I will generate a beautiful realistic image with natural lighting and high detail, rendered in good quality.

"} 
[2025-07-11 23:35:00] local.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-35-00_735e3730.png","size":1051711,"size_formatted":"1 MB","metadata":{"mime_type":"image/png","prompt":"a beautiful image","options":{"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}}} 
[2025-07-11 23:35:00] local.INFO: Image processed successfully {"part_index":1,"file_size":1051711,"path":"generated_images/gemini_2025-07-11_23-35-00_735e3730.png"} 
[2025-07-11 23:35:00] local.INFO: Image generation completed {"prompt":"a beautiful image","provider":"gemini","success":true,"images_count":1} 
[2025-07-11 23:35:00] local.INFO: Image generation started {"prompt":"صورة جميلة","provider":"gemini","options":{"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}} 
[2025-07-11 23:35:05] local.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content","finishReason","index"]} 
[2025-07-11 23:35:05] local.INFO: Text response from Gemini {"text":"I will generate a beautiful image, employing realistic techniques with natural light to highlight intricate details, ensuring a good quality final output.

"} 
[2025-07-11 23:35:05] local.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-35-05_a08c9a68.png","size":1312678,"size_formatted":"1.25 MB","metadata":{"mime_type":"image/png","prompt":"صورة جميلة","options":{"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}}} 
[2025-07-11 23:35:05] local.INFO: Image processed successfully {"part_index":1,"file_size":1312678,"path":"generated_images/gemini_2025-07-11_23-35-05_a08c9a68.png"} 
[2025-07-11 23:35:05] local.INFO: Image generation completed {"prompt":"صورة جميلة","provider":"gemini","success":true,"images_count":1} 
[2025-07-11 23:35:05] local.INFO: Image generation started {"prompt":"of a cat","provider":"gemini","options":{"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}} 
[2025-07-11 23:35:09] local.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content","finishReason","index"]} 
[2025-07-11 23:35:09] local.INFO: Text response from Gemini {"text":"I will generate a realistic image of a cat bathed in soft, natural light, captured with high detail to showcase its features.

"} 
[2025-07-11 23:35:09] local.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-35-09_3997d260.png","size":858532,"size_formatted":"838.41 KB","metadata":{"mime_type":"image/png","prompt":"of a cat","options":{"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}}} 
[2025-07-11 23:35:09] local.INFO: Image processed successfully {"part_index":1,"file_size":858532,"path":"generated_images/gemini_2025-07-11_23-35-09_3997d260.png"} 
[2025-07-11 23:35:09] local.INFO: Image generation completed {"prompt":"of a cat","provider":"gemini","success":true,"images_count":1} 
[2025-07-11 23:35:10] local.INFO: Free search completed {"query":"latest news","results_count":5} 
[2025-07-11 23:35:10] local.ERROR: Unsupported operand types: string + int {"exception":"[object] (TypeError(code: 0): Unsupported operand types: string + int at C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php:589)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(454): App\\Services\\ModelMergerService->handleSearch('latest news', Array)
#1 [internal function]: App\\Services\\ModelMergerService->detectAndProcessCommands('search for late...', Array)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\test_debug.php(34): ReflectionMethod->invoke(Object(App\\Services\\ModelMergerService), 'Search for late...', Array)
#3 {main}
"} 
[2025-07-11 23:35:48] local.INFO: Image generation started {"prompt":"a beautiful image","provider":"gemini","options":{"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}} 
[2025-07-11 23:35:50] local.INFO: Language detection {"message":"Generate a beautiful image","detected_code":"it","max_score":0.300710504727498,"result":{"it":0.300710504727498,"da":0.2954031159941477,"id":0.2951265295189976,"ro":0.29384906863249544,"no":0.2921475614989603,"de":0.29140174881287223,"nl":0.2894985494661803,"tl":0.2877216986625887,"af":0.2843412322234357,"fr":0.28103832502771686,"sw":0.2777130999555199,"es":0.27705165043945174,"en":0.2740402145925896,"sv":0.27173003992831873,"tr":0.2669085420260271,"pt":0.2654312740993157,"fi":0.2644339182596075,"so":0.2587713787085515,"et":0.25869756393018944,"hr":0.25488117350730044,"sq":0.24657151820493955,"hu":0.24589831730396441,"sl":0.23837588596856954,"lt":0.23459484087118654,"i-klingon":0.23391949523865163,"lv":0.22650845131393452,"sk":0.2176578236942366,"cs":0.2050076725524286,"pl":0.20483668720257384,"vi":0.17300622274530766,"th":0.015519755270958712,"pa":0.015490802981794984,"te":0.014253513825323938,"he":0.012810849327638516,"bn":0.012074612730815672,"ml":0.011507370190803879,"el":0.01096750298464403,"bg":0.010861156262206875,"mk":0.009272928870133531,"ta":0.009115831253934033,"ru":0.007282307141006884,"kn":0.005191652764078147,"uk":0.004574925339041736,"gu":0.0009867523544087604,"ar":0,"fa":0,"hi":0,"ja":0,"ko":0,"mr":0,"ne":0,"ur":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 23:35:50] local.INFO: Image generation started {"prompt":"a beautiful image","provider":"gemini","options":{"personality_prompt":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative.","target_language":"italian","target_language_code":"it","language_confidence":0.300710504727498,"max_tokens":2000,"temperature":0.7,"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}} 
[2025-07-11 23:35:52] local.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content","finishReason","index"]} 
[2025-07-11 23:35:52] local.INFO: Text response from Gemini {"text":"I will generate a beautiful image with a realistic style, featuring natural lighting and high detail, rendered in good quality.
"} 
[2025-07-11 23:35:52] local.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-35-52_c434b734.png","size":1316566,"size_formatted":"1.26 MB","metadata":{"mime_type":"image/png","prompt":"a beautiful image","options":{"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}}} 
[2025-07-11 23:35:52] local.INFO: Image processed successfully {"part_index":1,"file_size":1316566,"path":"generated_images/gemini_2025-07-11_23-35-52_c434b734.png"} 
[2025-07-11 23:35:52] local.INFO: Image generation completed {"prompt":"a beautiful image","provider":"gemini","success":true,"images_count":1} 
[2025-07-11 23:35:52] local.INFO: Image generation started {"prompt":"صورة جميلة","provider":"gemini","options":{"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}} 
[2025-07-11 23:35:55] local.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content","finishReason","index"]} 
[2025-07-11 23:35:55] local.INFO: Text response from Gemini {"text":"I will generate a beautiful image with a realistic style, natural soft lighting, and a high level of detail, ensuring good image quality.
"} 
[2025-07-11 23:35:55] local.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-35-55_eb766e3d.png","size":1311231,"size_formatted":"1.25 MB","metadata":{"mime_type":"image/png","prompt":"a beautiful image","options":{"personality_prompt":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative.","target_language":"italian","target_language_code":"it","language_confidence":0.300710504727498,"max_tokens":2000,"temperature":0.7,"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}}} 
[2025-07-11 23:35:55] local.INFO: Image processed successfully {"part_index":1,"file_size":1311231,"path":"generated_images/gemini_2025-07-11_23-35-55_eb766e3d.png"} 
[2025-07-11 23:35:55] local.INFO: Image generation completed {"prompt":"a beautiful image","provider":"gemini","success":true,"images_count":1} 
[2025-07-11 23:35:55] local.ERROR: Chat Controller Error {"message":"Undefined array key \"success\"","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined array...', 'C:\\\\Users\\\\<USER>\\\\...', 96)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(96): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined array...', 'C:\\\\Users\\\\<USER>\\\\...', 96)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(App\\Http\\Requests\\ChatRequest))
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#42 {main}","request_data":{"message":"Generate a beautiful image","session_id":"test-session-image","personality":"neutral","think_mode":false}} 
[2025-07-11 23:35:56] local.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content","finishReason","index"]} 
[2025-07-11 23:35:56] local.INFO: Text response from Gemini {"text":"I will generate a beautiful image, rendered in a realistic style with natural lighting and a high level of detail, ensuring good image quality.

"} 
[2025-07-11 23:35:56] local.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-35-56_666d9377.png","size":1438444,"size_formatted":"1.37 MB","metadata":{"mime_type":"image/png","prompt":"صورة جميلة","options":{"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}}} 
[2025-07-11 23:35:56] local.INFO: Image processed successfully {"part_index":1,"file_size":1438444,"path":"generated_images/gemini_2025-07-11_23-35-56_666d9377.png"} 
[2025-07-11 23:35:56] local.INFO: Image generation completed {"prompt":"صورة جميلة","provider":"gemini","success":true,"images_count":1} 
[2025-07-11 23:35:56] local.INFO: Image generation started {"prompt":"of a cat","provider":"gemini","options":{"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}} 
[2025-07-11 23:36:01] local.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content","finishReason","index"]} 
[2025-07-11 23:36:01] local.INFO: Text response from Gemini {"text":"A photorealistic image of a domestic cat with soft, striped fur, captured in natural sunlight that highlights the texture of its coat and the intricate details of its whiskers and eyes. The overall qu..."} 
[2025-07-11 23:36:01] local.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-36-01_46a62059.png","size":940799,"size_formatted":"918.75 KB","metadata":{"mime_type":"image/png","prompt":"of a cat","options":{"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}}} 
[2025-07-11 23:36:01] local.INFO: Image processed successfully {"part_index":1,"file_size":940799,"path":"generated_images/gemini_2025-07-11_23-36-01_46a62059.png"} 
[2025-07-11 23:36:01] local.INFO: Image generation completed {"prompt":"of a cat","provider":"gemini","success":true,"images_count":1} 
[2025-07-11 23:36:01] local.INFO: Free search cache hit {"query":"latest news"} 
[2025-07-11 23:36:01] local.ERROR: Undefined array key "type" {"exception":"[object] (ErrorException(code: 0): Undefined array key \"type\" at C:\\Users\\<USER>\\Desktop\\widdx-ai\\test_debug.php:37)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined array...', 'C:\\\\Users\\\\<USER>\\\\...', 37)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\test_debug.php(37): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined array...', 'C:\\\\Users\\\\<USER>\\\\...', 37)
#2 {main}
"} 
[2025-07-11 23:36:57] local.INFO: Language detection {"message":"مرحبا","detected_code":"ar","max_score":0.**********5835791,"result":{"ar":0.**********5835791,"fa":0.10429469856083184,"ur":0.09534008946245603,"so":0.0009493891797556719,"he":0.00024660361013562564,"et":8.336394469270356e-5,"tr":7.585657312061947e-5,"te":6.207755047068215e-5,"sw":5.9372756513809855e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"ro":4.5951028945866016e-5,"id":4.409251662944479e-5,"ko":4.0264005391582206e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"lv":3.423586686356094e-5,"sl":3.357427000042656e-5,"lt":1.7519586205815487e-5,"vi":1.7137537158470558e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 23:37:03] local.ERROR: Gemini API Error {"status":429,"body":"{
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"location\": \"global\",
              \"model\": \"gemini-1.5-flash\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"51s\"
      }
    ]
  }
}
"} 
[2025-07-11 23:37:03] local.ERROR: Gemini Client Error {"message":"Gemini API request failed: {
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"location\": \"global\",
              \"model\": \"gemini-1.5-flash\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"51s\"
      }
    ]
  }
}
","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(225): App\\Services\\GeminiClient->chat(Array, Array)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(83): App\\Services\\ModelMergerService->getModelResponses('You are WIDDX, ...', '\\xD9\\x85\\xD8\\xB1\\xD8\\xAD\\xD8\\xA8\\xD8\\xA7', Array, Array)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(90): App\\Services\\ModelMergerService->processMessage('\\xD9\\x85\\xD8\\xB1\\xD8\\xAD\\xD8\\xA8\\xD8\\xA7', Array, Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(App\\Http\\Requests\\ChatRequest))
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#43 {main}"} 
[2025-07-11 23:37:03] local.ERROR: Gemini API Error {"status":429,"body":"{
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"model\": \"gemini-1.5-flash\",
              \"location\": \"global\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"51s\"
      }
    ]
  }
}
"} 
[2025-07-11 23:37:03] local.ERROR: Gemini Client Error {"message":"Gemini API request failed: {
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"model\": \"gemini-1.5-flash\",
              \"location\": \"global\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"51s\"
      }
    ]
  }
}
","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(225): App\\Services\\GeminiClient->chat(Array, Array)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(83): App\\Services\\ModelMergerService->getModelResponses('You are WIDDX, ...', '\\xD9\\x85\\xD8\\xB1\\xD8\\xAD\\xD8\\xA8\\xD8\\xA7', Array, Array)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(90): App\\Services\\ModelMergerService->processMessage('\\xD9\\x85\\xD8\\xB1\\xD8\\xAD\\xD8\\xA8\\xD8\\xA7', Array, Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(App\\Http\\Requests\\ChatRequest))
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#43 {main}"} 
[2025-07-11 23:37:04] local.ERROR: Gemini API Error {"status":429,"body":"{
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"location\": \"global\",
              \"model\": \"gemini-1.5-flash\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"51s\"
      }
    ]
  }
}
"} 
[2025-07-11 23:37:04] local.ERROR: Gemini Client Error {"message":"Gemini API request failed: {
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"location\": \"global\",
              \"model\": \"gemini-1.5-flash\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"51s\"
      }
    ]
  }
}
","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(225): App\\Services\\GeminiClient->chat(Array, Array)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(83): App\\Services\\ModelMergerService->getModelResponses('You are WIDDX, ...', '\\xD9\\x85\\xD8\\xB1\\xD8\\xAD\\xD8\\xA8\\xD8\\xA7', Array, Array)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(90): App\\Services\\ModelMergerService->processMessage('\\xD9\\x85\\xD8\\xB1\\xD8\\xAD\\xD8\\xA8\\xD8\\xA7', Array, Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(App\\Http\\Requests\\ChatRequest))
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#43 {main}"} 
[2025-07-11 23:37:27] local.INFO: Image generation started {"prompt":"a beautiful image","provider":"gemini","options":{"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}} 
[2025-07-11 23:37:31] local.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content","finishReason","index"]} 
[2025-07-11 23:37:31] local.INFO: Text response from Gemini {"text":"I will generate a photorealistic image featuring a breathtaking natural landscape bathed in soft, golden hour light, with intricate details visible in the textures of the environment, ensuring a high-..."} 
[2025-07-11 23:37:31] local.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-37-31_83de1bfb.png","size":754370,"size_formatted":"736.69 KB","metadata":{"mime_type":"image/png","prompt":"a beautiful image","options":{"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}}} 
[2025-07-11 23:37:31] local.INFO: Image processed successfully {"part_index":1,"file_size":754370,"path":"generated_images/gemini_2025-07-11_23-37-31_83de1bfb.png"} 
[2025-07-11 23:37:31] local.INFO: Image generation completed {"prompt":"a beautiful image","provider":"gemini","success":true,"images_count":1} 
[2025-07-11 23:37:31] local.INFO: Image generation started {"prompt":"صورة جميلة","provider":"gemini","options":{"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}} 
[2025-07-11 23:37:37] local.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content","finishReason","index"]} 
[2025-07-11 23:37:37] local.INFO: Text response from Gemini {"text":"I will generate a beautiful image in a realistic style with natural lighting and high detail, ensuring good quality.

"} 
[2025-07-11 23:37:37] local.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-37-37_4d8d1c34.png","size":1199759,"size_formatted":"1.14 MB","metadata":{"mime_type":"image/png","prompt":"صورة جميلة","options":{"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}}} 
[2025-07-11 23:37:37] local.INFO: Image processed successfully {"part_index":1,"file_size":1199759,"path":"generated_images/gemini_2025-07-11_23-37-37_4d8d1c34.png"} 
[2025-07-11 23:37:37] local.INFO: Image generation completed {"prompt":"صورة جميلة","provider":"gemini","success":true,"images_count":1} 
[2025-07-11 23:37:37] local.INFO: Image generation started {"prompt":"of a cat","provider":"gemini","options":{"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}} 
[2025-07-11 23:37:41] local.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content","finishReason","index"]} 
[2025-07-11 23:37:41] local.INFO: Text response from Gemini {"text":"I will generate a photorealistic image of a domestic cat. The scene will be illuminated with soft, natural light, highlighting the intricate details of its fur, whiskers, and eyes. The overall image w..."} 
[2025-07-11 23:37:41] local.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-37-41_a5f36598.png","size":885045,"size_formatted":"864.3 KB","metadata":{"mime_type":"image/png","prompt":"of a cat","options":{"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}}} 
[2025-07-11 23:37:41] local.INFO: Image processed successfully {"part_index":1,"file_size":885045,"path":"generated_images/gemini_2025-07-11_23-37-41_a5f36598.png"} 
[2025-07-11 23:37:41] local.INFO: Image generation completed {"prompt":"of a cat","provider":"gemini","success":true,"images_count":1} 
[2025-07-11 23:37:41] local.INFO: Free search cache hit {"query":"latest news"} 
[2025-07-11 23:38:27] local.INFO: Language detection {"message":"Generate a beautiful image","detected_code":"it","max_score":0.300710504727498,"result":{"it":0.300710504727498,"da":0.2954031159941477,"id":0.2951265295189976,"ro":0.29384906863249544,"no":0.2921475614989603,"de":0.29140174881287223,"nl":0.2894985494661803,"tl":0.2877216986625887,"af":0.2843412322234357,"fr":0.28103832502771686,"sw":0.2777130999555199,"es":0.27705165043945174,"en":0.2740402145925896,"sv":0.27173003992831873,"tr":0.2669085420260271,"pt":0.2654312740993157,"fi":0.2644339182596075,"so":0.2587713787085515,"et":0.25869756393018944,"hr":0.25488117350730044,"sq":0.24657151820493955,"hu":0.24589831730396441,"sl":0.23837588596856954,"lt":0.23459484087118654,"i-klingon":0.23391949523865163,"lv":0.22650845131393452,"sk":0.2176578236942366,"cs":0.2050076725524286,"pl":0.20483668720257384,"vi":0.17300622274530766,"th":0.015519755270958712,"pa":0.015490802981794984,"te":0.014253513825323938,"he":0.012810849327638516,"bn":0.012074612730815672,"ml":0.011507370190803879,"el":0.01096750298464403,"bg":0.010861156262206875,"mk":0.009272928870133531,"ta":0.009115831253934033,"ru":0.007282307141006884,"kn":0.005191652764078147,"uk":0.004574925339041736,"gu":0.0009867523544087604,"ar":0,"fa":0,"hi":0,"ja":0,"ko":0,"mr":0,"ne":0,"ur":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 23:38:27] local.INFO: Image generation started {"prompt":"a beautiful image","provider":"gemini","options":{"personality_prompt":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative.","target_language":"italian","target_language_code":"it","language_confidence":0.300710504727498,"max_tokens":2000,"temperature":0.7,"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}} 
[2025-07-11 23:38:35] local.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content","finishReason","index"]} 
[2025-07-11 23:38:35] local.INFO: Text response from Gemini {"text":"I will generate a beautiful image with realistic style, natural lighting, and high detail, ensuring good quality. The scene will feature a sun-drenched meadow filled with wildflowers in full bloom, wi..."} 
[2025-07-11 23:38:35] local.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-38-35_4fb4c855.png","size":854315,"size_formatted":"834.29 KB","metadata":{"mime_type":"image/png","prompt":"a beautiful image","options":{"personality_prompt":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative.","target_language":"italian","target_language_code":"it","language_confidence":0.300710504727498,"max_tokens":2000,"temperature":0.7,"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}}} 
[2025-07-11 23:38:35] local.INFO: Image processed successfully {"part_index":1,"file_size":854315,"path":"generated_images/gemini_2025-07-11_23-38-35_4fb4c855.png"} 
[2025-07-11 23:38:35] local.INFO: Image generation completed {"prompt":"a beautiful image","provider":"gemini","success":true,"images_count":1} 
[2025-07-11 23:38:35] local.ERROR: Chat Controller Error {"message":"Undefined array key \"success\"","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined array...', 'C:\\\\Users\\\\<USER>\\\\...', 96)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(96): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined array...', 'C:\\\\Users\\\\<USER>\\\\...', 96)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(App\\Http\\Requests\\ChatRequest))
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#42 {main}","request_data":{"message":"Generate a beautiful image","session_id":"test-session-image","personality":"neutral","think_mode":false}} 
