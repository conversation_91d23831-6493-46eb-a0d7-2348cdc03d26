[2025-07-11 22:39:09] local.INFO: Language detection {"message":"مرحبا","detected_code":"ar","max_score":0.10665806315835791,"result":{"ar":0.10665806315835791,"fa":0.10429469856083184,"ur":0.09534008946245603,"so":0.0009493891797556719,"he":0.00024660361013562564,"et":8.336394469270356e-5,"tr":7.585657312061947e-5,"te":6.207755047068215e-5,"sw":5.9372756513809855e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"ro":4.5951028945866016e-5,"id":4.409251662944479e-5,"ko":4.0264005391582206e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"lv":3.423586686356094e-5,"sl":3.357427000042656e-5,"lt":1.7519586205815487e-5,"vi":1.7137537158470558e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 22:39:18] local.INFO: Gemini response successful {"attempt":1} 
[2025-07-11 22:39:29] local.INFO: Language detection {"message":"ماهي القدرات التي تتمتع بها","detected_code":"ar","max_score":0.30977663879167494,"result":{"ar":0.30977663879167494,"fa":0.2603015364711873,"ur":0.2182355814907419,"so":0.002363001745200698,"he":0.0005383816701455821,"et":0.0001916924931436499,"tr":0.0001607733849069996,"te":0.00010273653089592427,"ro":0.00010116949230098234,"sw":0.00010019152661705413,"ko":9.141967018228352e-5,"sl":7.276259826321953e-5,"lv":7.197140011761922e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"vi":3.640884368791714e-5,"tl":3.5574798256092454e-5,"lt":2.8021092557254597e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 22:39:53] local.INFO: Gemini response successful {"attempt":1} 
[2025-07-11 22:40:21] local.INFO: Language detection {"message":"هل تستطيع توليد الصور","detected_code":"ar","max_score":0.28236911534940295,"result":{"ar":0.28236911534940295,"fa":0.2412895289552115,"ur":0.20589679323025875,"so":0.0021291448516579407,"he":0.0004459053163447225,"et":0.00017720410703395544,"tr":0.0001607733849069996,"te":0.00010273653089592427,"sw":0.00010019152661705413,"ro":9.170898634153932e-5,"ko":8.811703797738934e-5,"lv":7.01454872182293e-5,"sl":6.439655065655586e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"vi":3.22086871366275e-5,"lt":2.146405444513652e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 22:40:34] local.INFO: Gemini response successful {"attempt":1} 
[2025-07-11 22:43:07] local.ERROR: PHP Parse error: Syntax error, unexpected '=' on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected '=' on line 1 at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php  = app(\\\\A...', false)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode(' = app(\\\\App\\\\Ser...', true)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode(' = app(\\\\App\\\\Ser...', true)
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute(' = app(\\\\App\\\\Ser...')
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
[2025-07-11 22:43:16] local.ERROR: There are no commands defined in the "test" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"test\" namespace. at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Application.php:659)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Application.php(708): Symfony\\Component\\Console\\Application->findNamespace('test')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('test:advanced-f...')
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-11 22:43:29] local.INFO: Free search completed {"query":"test query","results_count":5} 
[2025-07-11 22:43:29] local.INFO: Free image description generation started {"prompt":"beautiful sunset","options":{"style":"realistic","detail":"medium"}} 
[2025-07-11 22:43:42] local.INFO: Free TTS instructions generated {"text_length":21,"language":"en"} 
[2025-07-11 22:43:42] local.INFO: Deep search started {"query":"AI technology","options":{"language":"en","region":"US"}} 
[2025-07-11 22:43:48] local.INFO: Live search started (Free) {"query":"AI technology","options":{"max_results":5,"language":"en","region":"US"}} 
[2025-07-11 22:43:49] local.INFO: Free search completed {"query":"AI technology","results_count":5} 
[2025-07-11 22:43:49] local.INFO: Live search completed {"query":"AI technology","success":true,"results_count":5} 
[2025-07-11 22:43:49] local.INFO: Live search started (Free) {"query":"How does AI technology work?","options":{"max_results":5,"language":"en","region":"US"}} 
[2025-07-11 22:43:50] local.INFO: Free search completed {"query":"How does AI technology work?","results_count":5} 
[2025-07-11 22:43:50] local.INFO: Live search completed {"query":"How does AI technology work?","success":true,"results_count":5} 
[2025-07-11 22:43:50] local.INFO: Live search started (Free) {"query":"Applications of AI in healthcare","options":{"max_results":5,"language":"en","region":"US"}} 
[2025-07-11 22:43:51] local.INFO: Free search completed {"query":"Applications of AI in healthcare","results_count":5} 
[2025-07-11 22:43:51] local.INFO: Live search completed {"query":"Applications of AI in healthcare","success":true,"results_count":5} 
[2025-07-11 22:43:51] local.INFO: Live search started (Free) {"query":"Ethical concerns surrounding AI technology","options":{"max_results":5,"language":"en","region":"US"}} 
[2025-07-11 22:43:52] local.INFO: Free search completed {"query":"Ethical concerns surrounding AI technology","results_count":5} 
[2025-07-11 22:43:52] local.INFO: Live search completed {"query":"Ethical concerns surrounding AI technology","success":true,"results_count":5} 
[2025-07-11 22:43:52] local.INFO: Live search started (Free) {"query":"Latest advancements in AI technology 2023","options":{"max_results":5,"language":"en","region":"US"}} 
[2025-07-11 22:43:53] local.INFO: Free search completed {"query":"Latest advancements in AI technology 2023","results_count":5} 
[2025-07-11 22:43:53] local.INFO: Live search completed {"query":"Latest advancements in AI technology 2023","success":true,"results_count":5} 
[2025-07-11 22:43:53] local.ERROR: Deep search error {"query":"AI technology","error":"Trying to access array offset on value of type bool","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Trying to acces...', 'C:\\\\Users\\\\<USER>\\\\...', 212)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\DeepSearchService.php(212): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Trying to acces...', 'C:\\\\Users\\\\<USER>\\\\...', 212)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\DeepSearchService.php(50): App\\Services\\DeepSearchService->analyzeSearchResults(Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Console\\Commands\\TestAdvancedFeatures.php(241): App\\Services\\DeepSearchService->deepSearch('AI technology', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Console\\Commands\\TestAdvancedFeatures.php(96): App\\Console\\Commands\\TestAdvancedFeatures->testDeepSearch()
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Console\\Commands\\TestAdvancedFeatures.php(59): App\\Console\\Commands\\TestAdvancedFeatures->testAllFeatures()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\TestAdvancedFeatures->handle()
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\TestAdvancedFeatures), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}"} 
[2025-07-11 22:43:53] local.INFO: Think mode processing started {"message_length":12,"history_count":0} 
[2025-07-11 22:45:45] local.INFO: Think mode processing completed {"steps_count":5,"complexity_level":1,"success":true} 
[2025-07-11 22:47:37] local.INFO: Free image description generation started {"prompt":"beautiful sunset","options":{"style":"realistic","detail":"medium"}} 
[2025-07-11 22:47:42] local.INFO: Image generation started {"prompt":"A beautiful sunset over mountains","provider":"gemini","options":{"provider":"gemini","size":"1024x1024","style":"natural","quality":"standard"}} 
[2025-07-11 22:47:46] local.INFO: Image generation completed {"prompt":"A beautiful sunset over mountains","provider":"gemini","success":true,"images_count":1} 
[2025-07-11 22:49:09] local.INFO: Free image description generation started {"prompt":"beautiful sunset","options":{"style":"realistic","detail":"medium"}} 
[2025-07-11 22:49:13] local.INFO: Image generation started {"prompt":"A beautiful sunset over mountains","provider":"gemini","options":{"provider":"gemini","size":"1024x1024","style":"natural","quality":"standard"}} 
[2025-07-11 22:49:18] local.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content","finishReason","index"]} 
[2025-07-11 22:49:18] local.INFO: Text response from Gemini {"text":"I will generate a photorealistic image of a breathtaking sunset scene. The sky will be ablaze with warm hues of orange, pink, and gold, gradually fading into deeper blues and purples. Jagged mountain ..."} 
[2025-07-11 22:49:18] local.INFO: Image saved successfully {"path":"generated_images/gemini_2025-07-11_22-49-18_687194ee79bbe.png","size":753418,"provider":"gemini"} 
[2025-07-11 22:49:18] local.INFO: Image processed successfully {"part_index":1,"file_size":753418,"path":"generated_images/gemini_2025-07-11_22-49-18_687194ee79bbe.png"} 
[2025-07-11 22:49:18] local.INFO: Image generation completed {"prompt":"A beautiful sunset over mountains","provider":"gemini","success":true,"images_count":1} 
[2025-07-11 22:51:12] local.INFO: Language detection {"message":"hi","detected_code":"en","max_score":0.05160012052669572,"result":{"sw":0.05160012052669572,"sq":0.043470945670888154,"lt":0.04207354240756715,"de":0.04198868851629741,"fi":0.040856192399019935,"en":0.03997461911921514,"vi":0.0394393577129586,"it":0.03820003126534227,"et":0.03760605498632296,"hr":0.03617441077546277,"so":0.0353717277486911,"id":0.034857228341413325,"ro":0.0347732287760789,"af":0.03403485122591946,"pl":0.03267997440173418,"tr":0.032562791213769364,"sl":0.03222920768850783,"nl":0.031946678705619375,"lv":0.031388812175187214,"tl":0.030650318139233884,"fr":0.027509290282850263,"sv":0.026858840179172498,"pt":0.026565034063112017,"sk":0.026482493476614827,"da":0.02546515844521613,"no":0.025463885405603418,"es":0.025193121611995935,"i-klingon":0.025062803061284104,"cs":0.023833516933200943,"hu":0.02038579924121644,"pa":0.002415915507932945,"th":0.0022678535206016115,"te":0.002267464211929127,"ml":0.0018509648802696973,"he":0.001817570000254389,"bn":0.001759458651929844,"el":0.0016172881613752482,"bg":0.001616850208409635,"ta":0.0013873160351678833,"mk":0.0013738421782283803,"ru":0.0010162897971751875,"kn":0.0008158891364970348,"uk":0.0006829697735210298,"gu":0.0001753435410202518,"ar":0,"fa":0,"hi":0,"ja":0,"ko":0,"mr":0,"ne":0,"ur":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 22:51:20] local.INFO: Gemini response successful {"attempt":1} 
[2025-07-11 22:58:55] local.INFO: Free image description generation started {"prompt":"beautiful sunset","options":{"style":"realistic","detail":"medium"}} 
[2025-07-11 22:59:00] local.INFO: Image generation started {"prompt":"A beautiful sunset over mountains","provider":"gemini","options":{"provider":"gemini","size":"1024x1024","style":"natural","quality":"standard"}} 
[2025-07-11 22:59:04] local.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content","finishReason","index"]} 
[2025-07-11 22:59:04] local.INFO: Text response from Gemini {"text":"I will generate a realistic image of a breathtaking sunset painting the sky with vibrant oranges, pinks, and purples, casting a warm, golden light over a majestic range of sharply detailed mountains. ..."} 
[2025-07-11 22:59:04] local.INFO: Image saved successfully {"path":"generated_images/gemini_2025-07-11_22-59-04_68719738782cd.png","size":818445,"provider":"gemini"} 
[2025-07-11 22:59:04] local.INFO: Image processed successfully {"part_index":1,"file_size":818445,"path":"generated_images/gemini_2025-07-11_22-59-04_68719738782cd.png"} 
[2025-07-11 22:59:04] local.INFO: Image generation completed {"prompt":"A beautiful sunset over mountains","provider":"gemini","success":true,"images_count":1} 
[2025-07-11 23:01:40] local.INFO: Free image description generation started {"prompt":"beautiful sunset","options":{"style":"realistic","detail":"medium"}} 
[2025-07-11 23:01:45] local.INFO: Image generation started {"prompt":"A beautiful sunset over mountains","provider":"gemini","options":{"provider":"gemini","size":"1024x1024","style":"natural","quality":"standard"}} 
[2025-07-11 23:01:50] local.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content","finishReason","index"]} 
[2025-07-11 23:01:50] local.INFO: Text response from Gemini {"text":"I will generate a realistic image of a breathtaking sunset painting the sky with vibrant oranges, pinks, and purples above a majestic mountain range. The scene will be bathed in warm, natural light, h..."} 
[2025-07-11 23:01:50] local.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-01-50_4ea6e101.png","size":897039,"size_formatted":"876.01 KB","metadata":{"mime_type":"image/png","prompt":"A beautiful sunset over mountains","options":{"provider":"gemini","size":"1024x1024","style":"natural","quality":"standard"}}} 
[2025-07-11 23:01:50] local.INFO: Image processed successfully {"part_index":1,"file_size":897039,"path":"generated_images/gemini_2025-07-11_23-01-50_4ea6e101.png"} 
[2025-07-11 23:01:50] local.INFO: Image generation completed {"prompt":"A beautiful sunset over mountains","provider":"gemini","success":true,"images_count":1} 
[2025-07-11 23:04:53] testing.ERROR: Image generation failed {"prompt":"","error":"Invalid image prompt","arabic_error":"وصف الصورة غير صالح. يرجى إدخال وصف واضح ومفصل.","error_code":1005} 
[2025-07-11 23:04:53] testing.ERROR: Image generation failed {"prompt":"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa","error":"Prompt too long","arabic_error":"وصف الصورة طويل جداً. يرجى استخدام وصف أقصر (أقل من 1000 حرف).","error_code":1008} 
[2025-07-11 23:04:53] testing.ERROR: Image generation failed {"prompt":"test prompt","error":"Invalid image size","arabic_error":"حجم الصورة غير صالح. الأحجام المدعومة: 1024x1024, 1792x1024, 1024x1792","error_code":1009} 
[2025-07-11 23:04:53] testing.ERROR: Image generation failed {"prompt":"test prompt","error":"Invalid image style","arabic_error":"نمط الصورة غير صالح. الأنماط المدعومة: natural, vivid, photographic, artistic, digital-art","error_code":1010} 
[2025-07-11 23:04:53] testing.ERROR: Image generation failed {"prompt":"test prompt","error":"Invalid image quality","arabic_error":"جودة الصورة غير صالحة. الجودات المدعومة: standard, hd","error_code":1011} 
[2025-07-11 23:04:53] testing.ERROR: Image generation failed {"prompt":"test prompt","error":"Invalid image count","arabic_error":"عدد الصور غير صالح. يجب أن يكون بين 1 و 4.","error_code":1012} 
[2025-07-11 23:04:53] testing.INFO: Image generation started {"prompt":"test prompt","provider":"gemini","options":[]} 
[2025-07-11 23:04:58] testing.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content","finishReason","index"]} 
[2025-07-11 23:04:58] testing.INFO: Text response from Gemini {"text":"I will generate a photorealistic image based on your \"test prompt.\" The scene will be illuminated with soft, natural light, highlighting intricate details throughout the composition to achieve a good ..."} 
[2025-07-11 23:04:58] testing.ERROR: Failed to process image part {"part_index":1,"error":"Received Mockery_2_App_Services_ImageStorageService::saveImage(), but no expectations were specified"} 
[2025-07-11 23:04:58] testing.ERROR: Image generation failed {"prompt":"test prompt","error":"No images generated by Gemini","arabic_error":"لم يتم إنشاء أي صور. يرجى تجربة وصف مختلف للصورة.","error_code":1004} 
[2025-07-11 23:04:58] testing.INFO: Image generation started {"prompt":"test prompt","provider":"gemini","options":[]} 
[2025-07-11 23:04:58] testing.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content"]} 
[2025-07-11 23:04:58] testing.INFO: Image processed successfully {"part_index":0,"file_size":1024,"path":"generated_images/test.png"} 
[2025-07-11 23:04:58] testing.INFO: Image generation completed {"prompt":"test prompt","provider":"gemini","success":true,"images_count":1} 
[2025-07-11 23:04:58] testing.INFO: Image generation started {"prompt":"test prompt","provider":"gemini","options":[]} 
[2025-07-11 23:04:58] testing.ERROR: Gemini image generation failed {"status":400,"body":"{\"error\":{\"message\":\"API Error\"}}","error_data":{"error":{"message":"API Error"}},"prompt":"Generate an image: test prompt. Style: realistic, natural lighting, high detail. Quality: good quality."} 
[2025-07-11 23:04:58] testing.ERROR: Image generation failed {"prompt":"test prompt","error":"Gemini API error: API Error","arabic_error":"خطأ في خدمة توليد الصور: API Error","error_code":1007} 
[2025-07-11 23:04:58] testing.INFO: Image generation started {"prompt":"test prompt","provider":"gemini","options":[]} 
[2025-07-11 23:04:58] testing.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content"]} 
[2025-07-11 23:04:58] testing.ERROR: Image generation failed {"prompt":"test prompt","error":"No images generated by Gemini","arabic_error":"لم يتم إنشاء أي صور. يرجى تجربة وصف مختلف للصورة.","error_code":1004} 
[2025-07-11 23:04:58] testing.ERROR: Target class [App\Http\Controllers\FreeSearchService] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\FreeSearchService] does not exist. at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"prompt\":\"A be...')
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(49): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_generates_image_successfully()
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#62 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\FreeSearchService\" does not exist at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"prompt\":\"A be...')
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(49): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_generates_image_successfully()
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#62 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#63 {main}
"} 
[2025-07-11 23:04:58] testing.ERROR: Target class [App\Http\Controllers\FreeSearchService] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\FreeSearchService] does not exist. at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"style\":\"natur...')
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(81): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_validates_required_prompt()
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#62 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\FreeSearchService\" does not exist at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"style\":\"natur...')
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(81): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_validates_required_prompt()
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#62 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#63 {main}
"} 
[2025-07-11 23:04:58] testing.ERROR: Target class [App\Http\Controllers\FreeSearchService] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\FreeSearchService] does not exist. at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"prompt\":\"aaaa...')
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(96): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_validates_prompt_length()
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#62 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\FreeSearchService\" does not exist at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"prompt\":\"aaaa...')
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(96): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_validates_prompt_length()
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#62 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#63 {main}
"} 
[2025-07-11 23:04:58] testing.ERROR: Target class [App\Http\Controllers\FreeSearchService] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\FreeSearchService] does not exist. at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"prompt\":\"test...')
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(110): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_validates_image_size()
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#62 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\FreeSearchService\" does not exist at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"prompt\":\"test...')
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(110): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_validates_image_size()
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#62 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#63 {main}
"} 
[2025-07-11 23:04:58] testing.ERROR: Target class [App\Http\Controllers\FreeSearchService] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\FreeSearchService] does not exist. at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"prompt\":\"test...')
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(125): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_validates_image_style()
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#62 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\FreeSearchService\" does not exist at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"prompt\":\"test...')
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(125): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_validates_image_style()
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#62 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#63 {main}
"} 
[2025-07-11 23:04:58] testing.ERROR: Target class [App\Http\Controllers\FreeSearchService] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\FreeSearchService] does not exist. at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"prompt\":\"test...')
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(140): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_validates_image_quality()
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#62 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\FreeSearchService\" does not exist at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"prompt\":\"test...')
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(140): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_validates_image_quality()
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#62 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#63 {main}
"} 
[2025-07-11 23:04:58] testing.ERROR: Cannot assign null to property App\Services\GeminiClient::$apiKey of type string {"exception":"[object] (TypeError(code: 0): Cannot assign null to property App\\Services\\GeminiClient::$apiKey of type string at C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\GeminiClient.php:16)
[stacktrace]
#0 [internal function]: App\\Services\\GeminiClient->__construct()
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): ReflectionClass->newInstanceArgs(Array)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Services\\\\Ge...')
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Services\\\\Ge...', Array, true)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Services\\\\Ge...', Array)
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Services\\\\Ge...', Array)
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Services\\\\Ge...')
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Services\\\\Vo...')
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Services\\\\Vo...', Array, true)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Services\\\\Vo...', Array)
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Services\\\\Vo...', Array)
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Services\\\\Vo...')
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"prompt\":\"test...')
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(157): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_handles_missing_api_key()
#62 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#63 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#64 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#65 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#66 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#67 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#68 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#69 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#70 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#71 {main}
"} 
[2025-07-11 23:04:58] testing.ERROR: Target class [App\Http\Controllers\FreeSearchService] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\FreeSearchService] does not exist. at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"prompt\":\"test...')
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(177): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_handles_api_error()
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#62 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\FreeSearchService\" does not exist at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"prompt\":\"test...')
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(177): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_handles_api_error()
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#62 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#63 {main}
"} 
[2025-07-11 23:04:59] testing.ERROR: Target class [App\Http\Controllers\FreeSearchService] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\FreeSearchService] does not exist. at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"prompt\":\"test...')
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(197): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_handles_empty_response()
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#62 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\FreeSearchService\" does not exist at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"prompt\":\"test...')
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(197): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_handles_empty_response()
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#62 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#63 {main}
"} 
[2025-07-11 23:04:59] testing.ERROR: Target class [App\Http\Controllers\FreeSearchService] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\FreeSearchService] does not exist. at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"prompt\":\"test...')
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(234): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_uses_default_values()
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#62 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\FreeSearchService\" does not exist at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"prompt\":\"test...')
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(234): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_uses_default_values()
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#62 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#63 {main}
"} 
[2025-07-11 23:04:59] testing.ERROR: Target class [App\Http\Controllers\FreeSearchService] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\FreeSearchService] does not exist. at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"prompt\":\"\"}')
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(248): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_returns_arabic_error_messages()
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#62 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\FreeSearchService\" does not exist at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"prompt\":\"\"}')
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(248): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_returns_arabic_error_messages()
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#62 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#63 {main}
"} 
[2025-07-11 23:04:59] testing.ERROR: Target class [App\Http\Controllers\FreeSearchService] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\FreeSearchService] does not exist. at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"prompt\":\"test...')
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(290): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_saves_images_to_correct_location()
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#62 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\FreeSearchService\" does not exist at C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(573): Illuminate\\Foundation\\Testing\\TestCase->call('POST', '/api/features/g...', Array, Array, Array, Array, '{\"prompt\":\"test...')
#51 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(411): Illuminate\\Foundation\\Testing\\TestCase->json('POST', '/api/features/g...', Array, Array, 0)
#52 C:\\Users\\<USER>\\Desktop\\widdx-ai\\tests\\Feature\\ImageGenerationApiTest.php(290): Illuminate\\Foundation\\Testing\\TestCase->postJson('/api/features/g...', Array)
#53 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1656): Tests\\Feature\\ImageGenerationApiTest->it_saves_images_to_correct_location()
#54 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#55 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#56 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ImageGenerationApiTest))
#57 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#58 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#59 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#60 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#61 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#62 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\phpunit\\phpunit\\phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#63 {main}
"} 
[2025-07-11 23:05:48] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-05-48_8302b420.png","size":15,"size_formatted":"15 Bytes","metadata":{"mime_type":"image/png"}} 
[2025-07-11 23:05:48] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-05-48_abaf297d.png","size":0,"size_formatted":"0 Bytes","metadata":[]} 
[2025-07-11 23:05:49] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-05-49_7e35aa70.png","size":15,"size_formatted":"15 Bytes","metadata":[]} 
[2025-07-11 23:05:49] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-05-49_eba6e648.png","size":15,"size_formatted":"15 Bytes","metadata":[]} 
[2025-07-11 23:05:49] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-05-49_229c2f12.png","size":15,"size_formatted":"15 Bytes","metadata":[]} 
[2025-07-11 23:05:49] testing.INFO: Image cleanup completed {"deleted_files":0,"freed_space":"0 Bytes","cutoff_date":"2025-06-11"} 
[2025-07-11 23:05:49] testing.INFO: Image cleanup completed {"deleted_files":0,"freed_space":"0 Bytes","cutoff_date":"2025-06-11"} 
[2025-07-11 23:05:49] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-05-49_7df6ba37.jpeg","size":15,"size_formatted":"15 Bytes","metadata":{"mime_type":"image/jpeg"}} 
[2025-07-11 23:05:49] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-05-49_4982c11c.png","size":15,"size_formatted":"15 Bytes","metadata":[]} 
[2025-07-11 23:05:49] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-05-49_b99f45a1.png","size":15,"size_formatted":"15 Bytes","metadata":[]} 
[2025-07-11 23:05:49] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-05-49_3ddab065.png","size":15,"size_formatted":"15 Bytes","metadata":{"test":"metadata"}} 
[2025-07-11 23:08:02] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-08-02_dae4aca6.png","size":15,"size_formatted":"15 Bytes","metadata":{"mime_type":"image/png"}} 
[2025-07-11 23:08:02] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-08-02_65caa7b5.png","size":0,"size_formatted":"0 Bytes","metadata":[]} 
[2025-07-11 23:08:02] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-08-02_9426ec7c.png","size":15,"size_formatted":"15 Bytes","metadata":[]} 
[2025-07-11 23:08:02] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-08-02_37ca8108.png","size":15,"size_formatted":"15 Bytes","metadata":[]} 
[2025-07-11 23:08:02] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-08-02_819851b5.png","size":15,"size_formatted":"15 Bytes","metadata":[]} 
[2025-07-11 23:08:02] testing.INFO: Image cleanup completed {"deleted_files":0,"freed_space":"0 Bytes","cutoff_date":"2025-06-11"} 
[2025-07-11 23:08:02] testing.INFO: Image cleanup completed {"deleted_files":0,"freed_space":"0 Bytes","cutoff_date":"2025-06-11"} 
[2025-07-11 23:08:02] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-08-02_4d144095.jpeg","size":15,"size_formatted":"15 Bytes","metadata":{"mime_type":"image/jpeg"}} 
[2025-07-11 23:08:03] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-08-03_bc4211ee.png","size":15,"size_formatted":"15 Bytes","metadata":[]} 
[2025-07-11 23:08:03] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-08-03_ff67303e.png","size":15,"size_formatted":"15 Bytes","metadata":[]} 
[2025-07-11 23:08:03] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-08-03_5b739f3d.png","size":15,"size_formatted":"15 Bytes","metadata":{"test":"metadata"}} 
[2025-07-11 23:08:30] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-08-30_bbb6f0ba.png","size":15,"size_formatted":"15 Bytes","metadata":{"mime_type":"image/png"}} 
[2025-07-11 23:08:31] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-08-31_0f4e58db.png","size":15,"size_formatted":"15 Bytes","metadata":[]} 
[2025-07-11 23:08:31] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-08-31_6170df5f.png","size":15,"size_formatted":"15 Bytes","metadata":[]} 
[2025-07-11 23:08:31] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-08-31_8a3db6ed.png","size":15,"size_formatted":"15 Bytes","metadata":[]} 
[2025-07-11 23:08:31] testing.INFO: Image cleanup completed {"deleted_files":0,"freed_space":"0 Bytes","cutoff_date":"2025-06-11"} 
[2025-07-11 23:08:31] testing.INFO: Image cleanup completed {"deleted_files":0,"freed_space":"0 Bytes","cutoff_date":"2025-06-11"} 
[2025-07-11 23:08:31] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-08-31_c142c28d.jpeg","size":15,"size_formatted":"15 Bytes","metadata":{"mime_type":"image/jpeg"}} 
[2025-07-11 23:08:31] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-08-31_d8244347.png","size":15,"size_formatted":"15 Bytes","metadata":[]} 
[2025-07-11 23:08:31] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-08-31_63c03f37.png","size":15,"size_formatted":"15 Bytes","metadata":[]} 
[2025-07-11 23:08:31] testing.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-08-31_334a124f.png","size":15,"size_formatted":"15 Bytes","metadata":{"test":"metadata"}} 
[2025-07-11 23:08:46] testing.INFO: Image cleanup completed {"deleted_files":0,"freed_space":"0 Bytes","cutoff_date":"2025-06-11"} 
[2025-07-11 23:08:47] testing.INFO: Image cleanup completed {"deleted_files":0,"freed_space":"0 Bytes","cutoff_date":"2025-06-11"} 
[2025-07-11 23:08:47] testing.INFO: Image cleanup completed {"deleted_files":0,"freed_space":"0 Bytes","cutoff_date":"2025-06-11"} 
[2025-07-11 23:10:42] local.INFO: Language detection {"message":"قم بإنشاء صورة لمظهر طبيعي","detected_code":"ar","max_score":0.29222091119248267,"result":{"ar":0.29222091119248267,"fa":0.24372005418733553,"ur":0.2144876489324197,"so":0.0020802792321116927,"he":0.00042236752683104314,"tr":0.00019617067362140859,"et":0.0001726718426611792,"te":0.00010273653089592427,"sw":0.00010019152661705413,"ro":8.460395329444744e-5,"ko":8.031720447221372e-5,"lv":6.999332780994681e-5,"sl":6.27453570499775e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"vi":3.23434515179523e-5,"lt":3.1760653355571936e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 23:10:56] local.INFO: Gemini response successful {"attempt":1} 
[2025-07-11 23:12:07] local.INFO: Free image description generation started {"prompt":"beautiful sunset","options":{"style":"realistic","detail":"medium"}} 
[2025-07-11 23:12:12] local.INFO: Image generation started {"prompt":"A beautiful sunset over mountains","provider":"gemini","options":{"provider":"gemini","size":"1024x1024","style":"natural","quality":"standard"}} 
[2025-07-11 23:12:17] local.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content","finishReason","index"]} 
[2025-07-11 23:12:17] local.INFO: Text response from Gemini {"text":"Generating an image of a breathtaking sunset painting the sky with vibrant oranges, pinks, and purples, casting long shadows across a majestic mountain range. The scene will be rendered with realistic..."} 
[2025-07-11 23:12:17] local.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-12-17_ac4d205d.png","size":707303,"size_formatted":"690.73 KB","metadata":{"mime_type":"image/png","prompt":"A beautiful sunset over mountains","options":{"provider":"gemini","size":"1024x1024","style":"natural","quality":"standard"}}} 
[2025-07-11 23:12:17] local.INFO: Image processed successfully {"part_index":1,"file_size":707303,"path":"generated_images/gemini_2025-07-11_23-12-17_ac4d205d.png"} 
[2025-07-11 23:12:17] local.INFO: Image generation completed {"prompt":"A beautiful sunset over mountains","provider":"gemini","success":true,"images_count":1} 
[2025-07-11 23:16:21] local.INFO: Language detection {"message":"مرحبا","detected_code":"ar","max_score":0.10665806315835791,"result":{"ar":0.10665806315835791,"fa":0.10429469856083184,"ur":0.09534008946245603,"so":0.0009493891797556719,"he":0.00024660361013562564,"et":8.336394469270356e-5,"tr":7.585657312061947e-5,"te":6.207755047068215e-5,"sw":5.9372756513809855e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"ro":4.5951028945866016e-5,"id":4.409251662944479e-5,"ko":4.0264005391582206e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"lv":3.423586686356094e-5,"sl":3.357427000042656e-5,"lt":1.7519586205815487e-5,"vi":1.7137537158470558e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 23:16:27] local.INFO: Gemini response successful {"attempt":1} 
[2025-07-11 23:20:47] local.INFO: Language detection {"message":"مرحبا","detected_code":"ar","max_score":0.10665806315835791,"result":{"ar":0.10665806315835791,"fa":0.10429469856083184,"ur":0.09534008946245603,"so":0.0009493891797556719,"he":0.00024660361013562564,"et":8.336394469270356e-5,"tr":7.585657312061947e-5,"te":6.207755047068215e-5,"sw":5.9372756513809855e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"ro":4.5951028945866016e-5,"id":4.409251662944479e-5,"ko":4.0264005391582206e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"lv":3.423586686356094e-5,"sl":3.357427000042656e-5,"lt":1.7519586205815487e-5,"vi":1.7137537158470558e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 23:20:54] local.INFO: Gemini response successful {"attempt":1} 
[2025-07-11 23:22:22] local.INFO: Language detection {"message":"قم بتولي صورة لرجل يركب الجمل في الغابة","detected_code":"ar","max_score":0.3667346607601826,"result":{"ar":0.3667346607601826,"ur":0.24019283200287073,"fa":0.23376056602380724,"so":0.00237347294938918,"he":0.0005383816701455821,"et":0.00019065230132038979,"tr":0.0001607733849069996,"te":0.00010273653089592427,"sw":0.00010019152661705413,"ro":9.838926197820724e-5,"ko":9.268450804798767e-5,"lv":7.89707328986139e-5,"sl":7.303779719764926e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"id":4.409251662944479e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"vi":3.640884368791714e-5,"tl":3.5574798256092454e-5,"lt":2.8021092557254597e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 23:22:22] local.INFO: Image generation started {"prompt":"قم بتولي صورة لرجل يركب الجمل في الغابة","provider":"gemini","options":{"personality_prompt":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative.","target_language":"arabic","target_language_code":"ar","language_confidence":0.3667346607601826,"max_tokens":2000,"temperature":0.7,"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}} 
[2025-07-11 23:22:26] local.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content","finishReason","index"]} 
[2025-07-11 23:22:26] local.INFO: Text response from Gemini {"text":"I will generate a realistic image of a man riding a camel through a forest. The lighting will be natural, and the image will have a high level of detail and good quality.
"} 
[2025-07-11 23:22:26] local.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-22-26_8293d57d.png","size":1184832,"size_formatted":"1.13 MB","metadata":{"mime_type":"image/png","prompt":"قم بتولي صورة لرجل يركب الجمل في الغابة","options":{"personality_prompt":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative.","target_language":"arabic","target_language_code":"ar","language_confidence":0.3667346607601826,"max_tokens":2000,"temperature":0.7,"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}}} 
[2025-07-11 23:22:26] local.INFO: Image processed successfully {"part_index":1,"file_size":1184832,"path":"generated_images/gemini_2025-07-11_23-22-26_8293d57d.png"} 
[2025-07-11 23:22:26] local.INFO: Image generation completed {"prompt":"قم بتولي صورة لرجل يركب الجمل في الغابة","provider":"gemini","success":true,"images_count":1} 
[2025-07-11 23:22:26] local.ERROR: Chat Controller Error {"message":"Undefined array key \"success\"","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined array...', 'C:\\\\Users\\\\<USER>\\\\...', 96)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(96): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined array...', 'C:\\\\Users\\\\<USER>\\\\...', 96)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(App\\Http\\Requests\\ChatRequest))
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#42 {main}","request_data":{"message":"قم بتولي صورة لرجل يركب الجمل في الغابة","session_id":"session_1752276115283_g2u8d30wj","personality":"neutral","think_mode":false}} 
[2025-07-11 23:22:55] local.INFO: Language detection {"message":"Draw a beautiful cat","detected_code":"ro","max_score":0.2444844960649122,"result":{"ro":0.2444844960649122,"it":0.244388943340943,"sw":0.24285139642249456,"so":0.22688656195462478,"es":0.2261761442187784,"id":0.22447344665788146,"en":0.2196830111855252,"fr":0.21621101159341122,"i-klingon":0.21405620143716772,"pt":0.21346420839848296,"de":0.20847336208134198,"tr":0.20384539271274366,"nl":0.2030118121463415,"fi":0.2021873629042692,"da":0.2021259482290638,"af":0.20031791370626628,"no":0.19793362886232904,"sv":0.19739919116446195,"tl":0.19695275558520467,"et":0.19677070962480578,"hr":0.19392590098851104,"hu":0.19234700548290531,"pl":0.19146093628813968,"lv":0.1852098011961251,"lt":0.18411666118283126,"sq":0.18163297318090657,"sl":0.17867197250212247,"sk":0.1729252832934125,"cs":0.1610836004814553,"vi":0.1345491176863651,"th":0.012650671643727733,"pa":0.012244970288374072,"te":0.011681470286816435,"he":0.010383514784040329,"bn":0.009699985817345,"ml":0.009645815955930908,"el":0.008808712812067937,"bg":0.008720574028578218,"mk":0.007581661664673486,"ta":0.007459356762261224,"ru":0.005875969807478599,"kn":0.004272931829282978,"uk":0.003804284679313535,"gu":0.0007993375016501328,"ar":0,"fa":0,"hi":0,"ja":0,"ko":0,"mr":0,"ne":0,"ur":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 23:22:55] local.INFO: Image generation started {"prompt":"a beautiful cat","provider":"gemini","options":{"personality_prompt":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative.","target_language":"romanian","target_language_code":"ro","language_confidence":0.2444844960649122,"max_tokens":2000,"temperature":0.7,"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}} 
[2025-07-11 23:23:00] local.INFO: Gemini image generation response structure {"has_candidates":true,"candidates_count":1,"first_candidate_structure":["content","finishReason","index"]} 
[2025-07-11 23:23:00] local.INFO: Text response from Gemini {"text":"I will generate a photorealistic image of a beautiful cat. The cat will be depicted with highly detailed fur, showcasing individual strands and subtle variations in color. The lighting will be natural..."} 
[2025-07-11 23:23:00] local.INFO: Image saved to storage {"path":"generated_images/gemini_2025-07-11_23-23-00_56ab5524.png","size":1319895,"size_formatted":"1.26 MB","metadata":{"mime_type":"image/png","prompt":"a beautiful cat","options":{"personality_prompt":"You are WIDDX, an intelligent AI assistant. Respond in a balanced, professional, and helpful manner. Be clear, concise, and informative.","target_language":"romanian","target_language_code":"ro","language_confidence":0.2444844960649122,"max_tokens":2000,"temperature":0.7,"think_mode":false,"provider":"gemini","style":"natural","quality":"standard"}}} 
[2025-07-11 23:23:00] local.INFO: Image processed successfully {"part_index":1,"file_size":1319895,"path":"generated_images/gemini_2025-07-11_23-23-00_56ab5524.png"} 
[2025-07-11 23:23:00] local.INFO: Image generation completed {"prompt":"a beautiful cat","provider":"gemini","success":true,"images_count":1} 
[2025-07-11 23:23:00] local.ERROR: Chat Controller Error {"message":"Undefined array key \"success\"","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined array...', 'C:\\\\Users\\\\<USER>\\\\...', 96)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(96): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined array...', 'C:\\\\Users\\\\<USER>\\\\...', 96)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(App\\Http\\Requests\\ChatRequest))
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#42 {main}","request_data":{"message":"Draw a beautiful cat","session_id":"test-session-1","personality":"neutral","think_mode":false}} 
[2025-07-11 23:23:08] local.INFO: Language detection {"message":"مرحبا","detected_code":"ar","max_score":0.10665806315835791,"result":{"ar":0.10665806315835791,"fa":0.10429469856083184,"ur":0.09534008946245603,"so":0.0009493891797556719,"he":0.00024660361013562564,"et":8.336394469270356e-5,"tr":7.585657312061947e-5,"te":6.207755047068215e-5,"sw":5.9372756513809855e-5,"bn":5.912372307715048e-5,"ml":5.050307580022938e-5,"ro":4.5951028945866016e-5,"id":4.409251662944479e-5,"ko":4.0264005391582206e-5,"mr":4.015296481701335e-5,"mk":3.856232051858228e-5,"tl":3.5574798256092454e-5,"lv":3.423586686356094e-5,"sl":3.357427000042656e-5,"lt":1.7519586205815487e-5,"vi":1.7137537158470558e-5,"af":0,"bg":0,"cs":0,"da":0,"de":0,"el":0,"en":0,"es":0,"fi":0,"fr":0,"gu":0,"hi":0,"hr":0,"hu":0,"i-klingon":0,"it":0,"ja":0,"kn":0,"ne":0,"nl":0,"no":0,"pa":0,"pl":0,"pt":0,"ru":0,"sk":0,"sq":0,"sv":0,"ta":0,"th":0,"uk":0,"zh-cn":0,"zh-tw":0}} 
[2025-07-11 23:23:13] local.ERROR: Gemini API Error {"status":429,"body":"{
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"model\": \"gemini-1.5-flash\",
              \"location\": \"global\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"42s\"
      }
    ]
  }
}
"} 
[2025-07-11 23:23:13] local.ERROR: Gemini Client Error {"message":"Gemini API request failed: {
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"model\": \"gemini-1.5-flash\",
              \"location\": \"global\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"42s\"
      }
    ]
  }
}
","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(225): App\\Services\\GeminiClient->chat(Array, Array)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(83): App\\Services\\ModelMergerService->getModelResponses('You are WIDDX, ...', '\\xD9\\x85\\xD8\\xB1\\xD8\\xAD\\xD8\\xA8\\xD8\\xA7', Array, Array)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(90): App\\Services\\ModelMergerService->processMessage('\\xD9\\x85\\xD8\\xB1\\xD8\\xAD\\xD8\\xA8\\xD8\\xA7', Array, Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(App\\Http\\Requests\\ChatRequest))
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#43 {main}"} 
[2025-07-11 23:23:13] local.ERROR: Gemini API Error {"status":429,"body":"{
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"location\": \"global\",
              \"model\": \"gemini-1.5-flash\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"41s\"
      }
    ]
  }
}
"} 
[2025-07-11 23:23:13] local.ERROR: Gemini Client Error {"message":"Gemini API request failed: {
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"location\": \"global\",
              \"model\": \"gemini-1.5-flash\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"41s\"
      }
    ]
  }
}
","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(225): App\\Services\\GeminiClient->chat(Array, Array)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(83): App\\Services\\ModelMergerService->getModelResponses('You are WIDDX, ...', '\\xD9\\x85\\xD8\\xB1\\xD8\\xAD\\xD8\\xA8\\xD8\\xA7', Array, Array)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(90): App\\Services\\ModelMergerService->processMessage('\\xD9\\x85\\xD8\\xB1\\xD8\\xAD\\xD8\\xA8\\xD8\\xA7', Array, Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(App\\Http\\Requests\\ChatRequest))
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#43 {main}"} 
[2025-07-11 23:23:14] local.ERROR: Gemini API Error {"status":429,"body":"{
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"location\": \"global\",
              \"model\": \"gemini-1.5-flash\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"41s\"
      }
    ]
  }
}
"} 
[2025-07-11 23:23:14] local.ERROR: Gemini Client Error {"message":"Gemini API request failed: {
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\",
    \"details\": [
      {
        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",
        \"violations\": [
          {
            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_free_tier_requests\",
            \"quotaId\": \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\",
            \"quotaDimensions\": {
              \"location\": \"global\",
              \"model\": \"gemini-1.5-flash\"
            },
            \"quotaValue\": \"50\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.Help\",
        \"links\": [
          {
            \"description\": \"Learn more about Gemini API quotas\",
            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"
          }
        ]
      },
      {
        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",
        \"retryDelay\": \"41s\"
      }
    ]
  }
}
","trace":"#0 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(225): App\\Services\\GeminiClient->chat(Array, Array)
#1 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Services\\ModelMergerService.php(83): App\\Services\\ModelMergerService->getModelResponses('You are WIDDX, ...', '\\xD9\\x85\\xD8\\xB1\\xD8\\xAD\\xD8\\xA8\\xD8\\xA7', Array, Array)
#2 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Controllers\\ChatController.php(90): App\\Services\\ModelMergerService->processMessage('\\xD9\\x85\\xD8\\xB1\\xD8\\xAD\\xD8\\xA8\\xD8\\xA7', Array, Array)
#3 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ChatController->chat(Object(App\\Http\\Requests\\ChatRequest))
#4 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'chat')
#5 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\widdx-ai\\app\\Http\\Middleware\\WiddxRateLimitMiddleware.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\WiddxRateLimitMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\widdx-ai\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#43 {main}"} 
