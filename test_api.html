<!DOCTYPE html>
<html>
<head>
    <title>Test WIDDX API</title>
    <meta name="csrf-token" content="">
</head>
<body>
    <h1>Test WIDDX API</h1>
    <button onclick="testImageGeneration()">Test Image Generation</button>
    <button onclick="testNormalChat()">Test Normal Chat</button>
    <div id="result"></div>

    <script>
        async function testImageGeneration() {
            const result = document.getElementById('result');
            result.innerHTML = 'Testing image generation...';
            
            try {
                const response = await fetch('http://localhost:8000/api/chat/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        message: "Generate a beautiful image",
                        session_id: "test-session-image",
                        personality: "neutral",
                        think_mode: false
                    })
                });
                
                const data = await response.json();
                result.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                result.innerHTML = 'Error: ' + error.message;
            }
        }
        
        async function testNormalChat() {
            const result = document.getElementById('result');
            result.innerHTML = 'Testing normal chat...';
            
            try {
                const response = await fetch('http://localhost:8000/api/chat/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        message: "Hello, how are you?",
                        session_id: "test-session-normal",
                        personality: "neutral",
                        think_mode: false
                    })
                });
                
                const data = await response.json();
                result.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                result.innerHTML = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
