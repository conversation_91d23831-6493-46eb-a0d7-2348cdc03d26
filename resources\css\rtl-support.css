/* RTL Support for WIDDX AI - Forceful Implementation */

/* Force RTL on all RTL messages */
.widdx-message-container.rtl-message,
.widdx-message-container.rtl-message *:not(.widdx-message-actions) {
  direction: rtl !important;
  text-align: right !important;
  unicode-bidi: plaintext !important;
}

/* Force RTL on message content */
.widdx-message-container.rtl-message .widdx-message-content {
  direction: rtl !important;
  text-align: right !important;
}

/* Force RTL on message text */
.widdx-message-container.rtl-message .widdx-message-text {
  direction: rtl !important;
  text-align: right !important;
  unicode-bidi: plaintext !important;
  display: inline-block !important;
  width: 100% !important;
}

/* Force LTR for message actions */
.widdx-message-container.rtl-message .widdx-message-actions {
  direction: ltr !important;
  text-align: left !important;
  justify-content: flex-start !important;
}

/* Force RTL for input fields */
.widdx-input[dir="rtl"],
.widdx-textarea[dir="rtl"] {
  text-align: right !important;
  direction: rtl !important;
  unicode-bidi: plaintext !important;
}

/* Force RTL for message header */
.widdx-message-container.rtl-message .widdx-message-header {
  flex-direction: row-reverse !important;
  justify-content: flex-end !important;
}

/* Force RTL for timestamps */
.widdx-message-container.rtl-message .widdx-message-time {
  margin-left: 0 !important;
  margin-right: 0.5rem !important;
}

/* Force LTR for code blocks */
.widdx-message-container.rtl-message pre,
.widdx-message-container.rtl-message code {
  direction: ltr !important;
  text-align: left !important;
  unicode-bidi: embed !important;
}

/* Force RTL on the message body */
.widdx-message-container.rtl-message .widdx-message-body {
  direction: rtl !important;
  text-align: right !important;
  unicode-bidi: plaintext !important;
}

/* Force RTL on the message container */
.widdx-message-container.rtl-message {
  direction: rtl !important;
  text-align: right !important;
  unicode-bidi: plaintext !important;
}
